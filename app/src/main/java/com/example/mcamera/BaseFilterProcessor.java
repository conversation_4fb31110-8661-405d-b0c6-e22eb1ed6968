package com.example.mcamera;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;

/**
 * 基础滤镜处理器实现
 * 提供了通用的滤镜处理逻辑
 */
public abstract class BaseFilterProcessor implements FilterProcessor {
    protected FilterType filterType;
    protected ColorMatrix colorMatrix;

    public BaseFilterProcessor(FilterType filterType) {
        this.filterType = filterType;
        this.colorMatrix = createColorMatrix();
    }

    @Override
    public Bitmap applyFilter(Bitmap bitmap) {
        if (bitmap == null || bitmap.isRecycled()) {
            return null;
        }

        // 如果是无滤镜，直接返回原图
        if (filterType == FilterType.NONE) {
            return bitmap;
        }

        // 创建新的位图
        Bitmap filteredBitmap = Bitmap.createBitmap(
                bitmap.getWidth(), 
                bitmap.getHeight(), 
                Bitmap.Config.ARGB_8888
        );

        Canvas canvas = new Canvas(filteredBitmap);
        Paint paint = new Paint();

        // 应用颜色矩阵滤镜
        if (colorMatrix != null) {
            ColorMatrixColorFilter filter = new ColorMatrixColorFilter(colorMatrix);
            paint.setColorFilter(filter);
        }

        canvas.drawBitmap(bitmap, 0, 0, paint);
        return filteredBitmap;
    }

    @Override
    public ColorMatrix getColorMatrix() {
        return colorMatrix;
    }

    @Override
    public FilterType getFilterType() {
        return filterType;
    }

    /**
     * 子类需要实现此方法来创建特定的颜色矩阵
     * @return ColorMatrix对象
     */
    protected abstract ColorMatrix createColorMatrix();
}
