package com.example.mcamera;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.ColorMatrix;

/**
 * 富士胶片模拟滤镜处理器
 * 实现富士相机经典的胶片模拟效果
 */
public abstract class FujiFilmSimulationProcessor extends BaseFilterProcessor {

    public FujiFilmSimulationProcessor(FilterType filterType) {
        super(filterType);
    }

    @Override
    public Bitmap applyFilter(Bitmap bitmap) {
        if (bitmap == null || bitmap.isRecycled()) {
            return null;
        }

        // 如果是无滤镜，直接返回原图
        if (filterType == FilterType.NONE) {
            return bitmap;
        }

        // 创建新的位图
        Bitmap filteredBitmap = Bitmap.createBitmap(
                bitmap.getWidth(), 
                bitmap.getHeight(), 
                Bitmap.Config.ARGB_8888
        );

        // 逐像素处理
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int[] pixels = new int[width * height];
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height);

        for (int i = 0; i < pixels.length; i++) {
            pixels[i] = processPixel(pixels[i]);
        }

        filteredBitmap.setPixels(pixels, 0, width, 0, 0, width, height);
        return filteredBitmap;
    }

    /**
     * 处理单个像素，子类需要实现具体的胶片模拟效果
     */
    protected abstract int processPixel(int pixel);

    /**
     * Provia胶片模拟处理器 - 标准色彩，自然饱和度
     */
    public static class ProviaProcessor extends FujiFilmSimulationProcessor {
        public ProviaProcessor() {
            super(FilterType.FUJI_PROVIA);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            // Provia的色彩矩阵 - 略微增强对比度和饱和度
            return new ColorMatrix(new float[]{
                    1.1f, 0.05f, 0.05f, 0, 0,
                    0.05f, 1.1f, 0.05f, 0, 0,
                    0.05f, 0.05f, 1.1f, 0, 0,
                    0, 0, 0, 1, 0
            });
        }

        @Override
        protected int processPixel(int pixel) {
            // 轻微增强饱和度和对比度
            pixel = ColorUtils.adjustSaturation(pixel, 1.15f);
            pixel = ColorUtils.applySCurve(pixel, 0.1f);
            return pixel;
        }
    }

    /**
     * Velvia胶片模拟处理器 - 高饱和度，鲜艳色彩
     */
    public static class VelviaProcessor extends FujiFilmSimulationProcessor {
        public VelviaProcessor() {
            super(FilterType.FUJI_VELVIA);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            // Velvia的色彩矩阵 - 高饱和度，特别增强红色和绿色
            return new ColorMatrix(new float[]{
                    1.3f, 0.1f, 0.1f, 0, 0,
                    0.1f, 1.4f, 0.1f, 0, 0,
                    0.05f, 0.05f, 1.2f, 0, 0,
                    0, 0, 0, 1, 0
            });
        }

        @Override
        protected int processPixel(int pixel) {
            // 高饱和度处理
            pixel = ColorUtils.adjustSaturation(pixel, 1.4f);
            // 增强对比度
            pixel = ColorUtils.applySCurve(pixel, 0.2f);
            // 轻微暖色调
            pixel = ColorUtils.adjustTemperature(pixel, 0.1f);
            return pixel;
        }
    }

    /**
     * Astia胶片模拟处理器 - 柔和色调，适合人像
     */
    public static class AstiaProcessor extends FujiFilmSimulationProcessor {
        public AstiaProcessor() {
            super(FilterType.FUJI_ASTIA);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            // Astia的色彩矩阵 - 柔和，肤色友好
            return new ColorMatrix(new float[]{
                    1.05f, 0.1f, 0.05f, 0, 5,
                    0.05f, 1.0f, 0.05f, 0, 5,
                    0.05f, 0.05f, 0.95f, 0, 5,
                    0, 0, 0, 1, 0
            });
        }

        @Override
        protected int processPixel(int pixel) {
            // 柔和处理
            pixel = ColorUtils.adjustSaturation(pixel, 1.05f);
            // 轻微提亮
            pixel = ColorUtils.adjustBrightness(pixel, 1.05f);
            // 暖色调，适合肤色
            pixel = ColorUtils.adjustTemperature(pixel, 0.15f);
            return pixel;
        }
    }

    /**
     * Classic Chrome胶片模拟处理器 - 低饱和度，复古色调
     */
    public static class ClassicChromeProcessor extends FujiFilmSimulationProcessor {
        public ClassicChromeProcessor() {
            super(FilterType.FUJI_CLASSIC_CHROME);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            // Classic Chrome的色彩矩阵 - 低饱和度，银色高光
            return new ColorMatrix(new float[]{
                    0.9f, 0.1f, 0.1f, 0, 0,
                    0.1f, 0.85f, 0.1f, 0, 0,
                    0.1f, 0.1f, 0.8f, 0, 0,
                    0, 0, 0, 1, 0
            });
        }

        @Override
        protected int processPixel(int pixel) {
            // 降低饱和度
            pixel = ColorUtils.adjustSaturation(pixel, 0.7f);
            // 色彩分离效果 - 高光偏银色，阴影偏暖
            pixel = ColorUtils.colorGrading(pixel, 
                Color.rgb(200, 200, 210), // 银色高光
                Color.rgb(140, 120, 100), // 暖色阴影
                0.5f);
            return pixel;
        }
    }

    /**
     * Pro Neg Hi胶片模拟处理器 - 专业负片，高对比度
     */
    public static class ProNegHiProcessor extends FujiFilmSimulationProcessor {
        public ProNegHiProcessor() {
            super(FilterType.FUJI_PRO_NEG_HI);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            // Pro Neg Hi的色彩矩阵 - 专业负片风格
            return new ColorMatrix(new float[]{
                    1.2f, 0.05f, 0.05f, 0, -10,
                    0.05f, 1.15f, 0.05f, 0, -5,
                    0.05f, 0.05f, 1.1f, 0, -5,
                    0, 0, 0, 1, 0
            });
        }

        @Override
        protected int processPixel(int pixel) {
            // 增强对比度
            pixel = ColorUtils.applySCurve(pixel, 0.25f);
            // 轻微降低饱和度
            pixel = ColorUtils.adjustSaturation(pixel, 0.95f);
            // 冷色调
            pixel = ColorUtils.adjustTemperature(pixel, -0.1f);
            return pixel;
        }
    }

    /**
     * Pro Neg Std胶片模拟处理器 - 专业负片，标准对比度
     */
    public static class ProNegStdProcessor extends FujiFilmSimulationProcessor {
        public ProNegStdProcessor() {
            super(FilterType.FUJI_PRO_NEG_STD);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            // Pro Neg Std的色彩矩阵
            return new ColorMatrix(new float[]{
                    1.1f, 0.05f, 0.05f, 0, -5,
                    0.05f, 1.05f, 0.05f, 0, -3,
                    0.05f, 0.05f, 1.0f, 0, -3,
                    0, 0, 0, 1, 0
            });
        }

        @Override
        protected int processPixel(int pixel) {
            // 中等对比度
            pixel = ColorUtils.applySCurve(pixel, 0.15f);
            // 保持自然饱和度
            pixel = ColorUtils.adjustSaturation(pixel, 1.0f);
            return pixel;
        }
    }

    /**
     * Classic Neg胶片模拟处理器 - 经典负片风格
     */
    public static class ClassicNegProcessor extends FujiFilmSimulationProcessor {
        public ClassicNegProcessor() {
            super(FilterType.FUJI_CLASSIC_NEG);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            // Classic Neg的色彩矩阵
            return new ColorMatrix(new float[]{
                    1.0f, 0.1f, 0.1f, 0, 0,
                    0.1f, 1.0f, 0.1f, 0, 0,
                    0.1f, 0.1f, 0.9f, 0, 0,
                    0, 0, 0, 1, 0
            });
        }

        @Override
        protected int processPixel(int pixel) {
            // 经典负片效果
            pixel = ColorUtils.adjustSaturation(pixel, 1.1f);
            // 轻微胶片颗粒
            pixel = ColorUtils.addFilmGrain(pixel, 0.1f);
            // 暖色调
            pixel = ColorUtils.adjustTemperature(pixel, 0.05f);
            return pixel;
        }
    }
}
