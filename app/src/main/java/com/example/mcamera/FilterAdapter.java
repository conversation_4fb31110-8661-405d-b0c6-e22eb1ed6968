package com.example.mcamera;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

/**
 * 滤镜选择器适配器
 */
public class FilterAdapter extends RecyclerView.Adapter<FilterAdapter.FilterViewHolder> {
    private List<FilterType> filterTypes;
    private Context context;
    private FilterType selectedFilter;
    private OnFilterSelectedListener listener;
    private Bitmap previewBitmap;

    public interface OnFilterSelectedListener {
        void onFilterSelected(FilterType filterType);
    }

    public FilterAdapter(Context context, List<FilterType> filterTypes) {
        this.context = context;
        this.filterTypes = filterTypes;
        this.selectedFilter = FilterType.NONE;
        // 创建一个简单的预览位图
        createPreviewBitmap();
    }

    private void createPreviewBitmap() {
        // 创建一个简单的渐变预览图
        previewBitmap = Bitmap.createBitmap(48, 48, Bitmap.Config.ARGB_8888);
        // 这里可以加载一个实际的预览图片，暂时使用简单的颜色填充
        previewBitmap.eraseColor(0xFF808080); // 灰色
    }

    public void setOnFilterSelectedListener(OnFilterSelectedListener listener) {
        this.listener = listener;
    }

    public void setSelectedFilter(FilterType filterType) {
        this.selectedFilter = filterType;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public FilterViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.filter_item, parent, false);
        return new FilterViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FilterViewHolder holder, int position) {
        FilterType filterType = filterTypes.get(position);
        
        // 设置滤镜名称
        holder.filterName.setText(context.getString(filterType.getDisplayNameResId()));
        
        // 应用滤镜到预览图
        FilterProcessor processor = FilterManager.getInstance().getFilterProcessor(filterType);
        if (processor != null && previewBitmap != null) {
            Bitmap filteredPreview = processor.applyFilter(previewBitmap);
            if (filteredPreview != null) {
                holder.filterPreview.setImageBitmap(filteredPreview);
            }
        }
        
        // 设置选中状态
        if (filterType == selectedFilter) {
            holder.filterPreview.setBackground(
                ContextCompat.getDrawable(context, R.drawable.filter_preview_selected)
            );
        } else {
            holder.filterPreview.setBackground(
                ContextCompat.getDrawable(context, R.drawable.filter_preview_background)
            );
        }
        
        // 设置点击监听器
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onFilterSelected(filterType);
            }
            setSelectedFilter(filterType);
        });
    }

    @Override
    public int getItemCount() {
        return filterTypes.size();
    }

    static class FilterViewHolder extends RecyclerView.ViewHolder {
        ImageView filterPreview;
        TextView filterName;

        public FilterViewHolder(@NonNull View itemView) {
            super(itemView);
            filterPreview = itemView.findViewById(R.id.filter_preview);
            filterName = itemView.findViewById(R.id.filter_name);
        }
    }
}
