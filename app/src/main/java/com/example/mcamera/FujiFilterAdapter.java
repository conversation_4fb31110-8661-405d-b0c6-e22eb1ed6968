package com.example.mcamera;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 富士滤镜选择器适配器
 * 专门为富士胶片模拟滤镜设计的适配器
 */
public class FujiFilterAdapter extends RecyclerView.Adapter<FujiFilterAdapter.FujiFilterViewHolder> {
    private List<FilterType> filterTypes;
    private Context context;
    private FilterType selectedFilter;
    private OnFilterSelectedListener listener;
    private Bitmap previewBitmap;
    private Map<FilterType, String> filmTypeMap;

    public interface OnFilterSelectedListener {
        void onFilterSelected(FilterType filterType);
    }

    public FujiFilterAdapter(Context context, List<FilterType> filterTypes) {
        this.context = context;
        this.filterTypes = filterTypes;
        this.selectedFilter = FilterType.NONE;
        createPreviewBitmap();
        initFilmTypeMap();
    }

    private void createPreviewBitmap() {
        // 创建一个富士风格的预览图
        previewBitmap = Bitmap.createBitmap(48, 48, Bitmap.Config.ARGB_8888);
        // 创建一个渐变效果作为预览
        int[] colors = new int[48 * 48];
        for (int y = 0; y < 48; y++) {
            for (int x = 0; x < 48; x++) {
                int r = (int) (128 + 127 * Math.sin(x * 0.1));
                int g = (int) (128 + 127 * Math.sin(y * 0.1));
                int b = (int) (128 + 127 * Math.sin((x + y) * 0.05));
                colors[y * 48 + x] = android.graphics.Color.rgb(r, g, b);
            }
        }
        previewBitmap.setPixels(colors, 0, 48, 0, 0, 48, 48);
    }

    private void initFilmTypeMap() {
        filmTypeMap = new HashMap<>();
        filmTypeMap.put(FilterType.FUJI_PROVIA, "RDPIII");
        filmTypeMap.put(FilterType.FUJI_VELVIA, "RVP");
        filmTypeMap.put(FilterType.FUJI_ASTIA, "RAP");
        filmTypeMap.put(FilterType.FUJI_CLASSIC_CHROME, "CC");
        filmTypeMap.put(FilterType.FUJI_PRO_NEG_HI, "NPH");
        filmTypeMap.put(FilterType.FUJI_PRO_NEG_STD, "NPS");
        filmTypeMap.put(FilterType.FUJI_CLASSIC_NEG, "CN");
    }

    public void setOnFilterSelectedListener(OnFilterSelectedListener listener) {
        this.listener = listener;
    }

    public void setSelectedFilter(FilterType filterType) {
        this.selectedFilter = filterType;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public FujiFilterViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.fuji_filter_item, parent, false);
        return new FujiFilterViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FujiFilterViewHolder holder, int position) {
        FilterType filterType = filterTypes.get(position);
        
        // 设置滤镜名称
        String filterName = context.getString(filterType.getDisplayNameResId());
        holder.filterName.setText(filterName);
        
        // 设置胶片类型标识（仅富士滤镜显示）
        if (isFujiFilter(filterType)) {
            String filmType = filmTypeMap.get(filterType);
            if (filmType != null) {
                holder.filmType.setText(filmType);
                holder.filmType.setVisibility(View.VISIBLE);
                holder.fujiIndicator.setVisibility(View.VISIBLE);
            }
        } else {
            holder.filmType.setVisibility(View.GONE);
            holder.fujiIndicator.setVisibility(View.GONE);
        }
        
        // 应用滤镜到预览图
        FilterProcessor processor = FilterManager.getInstance().getFilterProcessor(filterType);
        if (processor != null && previewBitmap != null) {
            Bitmap filteredPreview = processor.applyFilter(previewBitmap);
            if (filteredPreview != null) {
                holder.filterPreview.setImageBitmap(filteredPreview);
            }
        }
        
        // 设置选中状态
        if (filterType == selectedFilter) {
            holder.itemView.setBackground(
                ContextCompat.getDrawable(context, R.drawable.fuji_filter_preview_selected)
            );
            holder.fujiIndicator.setVisibility(View.VISIBLE);
        } else {
            holder.itemView.setBackground(
                ContextCompat.getDrawable(context, R.drawable.fuji_filter_preview_background)
            );
            if (!isFujiFilter(filterType)) {
                holder.fujiIndicator.setVisibility(View.GONE);
            }
        }
        
        // 设置点击监听器
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onFilterSelected(filterType);
            }
            setSelectedFilter(filterType);
        });
    }

    @Override
    public int getItemCount() {
        return filterTypes.size();
    }

    private boolean isFujiFilter(FilterType filterType) {
        return filterType.getName().startsWith("Fuji");
    }

    static class FujiFilterViewHolder extends RecyclerView.ViewHolder {
        ImageView filterPreview;
        TextView filterName;
        TextView filmType;
        View fujiIndicator;

        public FujiFilterViewHolder(@NonNull View itemView) {
            super(itemView);
            filterPreview = itemView.findViewById(R.id.filter_preview);
            filterName = itemView.findViewById(R.id.filter_name);
            filmType = itemView.findViewById(R.id.film_type);
            fujiIndicator = itemView.findViewById(R.id.fuji_indicator);
        }
    }
}
