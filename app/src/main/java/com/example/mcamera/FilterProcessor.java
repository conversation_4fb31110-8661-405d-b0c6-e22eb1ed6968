package com.example.mcamera;

import android.graphics.Bitmap;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;

/**
 * 滤镜处理器接口
 * 定义了滤镜处理的基本方法
 */
public interface FilterProcessor {
    /**
     * 应用滤镜到位图
     * @param bitmap 原始位图
     * @return 应用滤镜后的位图
     */
    Bitmap applyFilter(Bitmap bitmap);

    /**
     * 获取滤镜的ColorMatrix
     * @return ColorMatrix对象，如果不支持则返回null
     */
    ColorMatrix getColorMatrix();

    /**
     * 获取滤镜类型
     * @return 滤镜类型
     */
    FilterType getFilterType();
}
