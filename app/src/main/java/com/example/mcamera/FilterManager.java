package com.example.mcamera;

import android.graphics.Bitmap;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import androidx.camera.core.ImageAnalysis;
import androidx.camera.core.ImageProxy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 滤镜管理器
 * 负责管理所有滤镜处理器和当前应用的滤镜
 */
public class FilterManager {
    private static FilterManager instance;
    private Map<FilterType, FilterProcessor> filterProcessors;
    private FilterType currentFilterType;
    private FilterProcessor currentFilterProcessor;

    private FilterManager() {
        initializeFilters();
        currentFilterType = FilterType.NONE;
        currentFilterProcessor = filterProcessors.get(FilterType.NONE);
    }

    public static synchronized FilterManager getInstance() {
        if (instance == null) {
            instance = new FilterManager();
        }
        return instance;
    }

    /**
     * 初始化所有滤镜处理器
     */
    private void initializeFilters() {
        filterProcessors = new HashMap<>();
        // 基础滤镜
        filterProcessors.put(FilterType.NONE, new FilterProcessors.NoneFilterProcessor());
        filterProcessors.put(FilterType.VINTAGE, new FilterProcessors.VintageFilterProcessor());
        filterProcessors.put(FilterType.BLACK_WHITE, new FilterProcessors.BlackWhiteFilterProcessor());
        filterProcessors.put(FilterType.SEPIA, new FilterProcessors.SepiaFilterProcessor());
        filterProcessors.put(FilterType.COOL, new FilterProcessors.CoolFilterProcessor());
        filterProcessors.put(FilterType.WARM, new FilterProcessors.WarmFilterProcessor());
        filterProcessors.put(FilterType.DRAMATIC, new FilterProcessors.DramaticFilterProcessor());
        filterProcessors.put(FilterType.VIVID, new FilterProcessors.VividFilterProcessor());
        filterProcessors.put(FilterType.SOFT, new FilterProcessors.SoftFilterProcessor());

        // 富士胶片模拟滤镜
        filterProcessors.put(FilterType.FUJI_PROVIA, new FilterProcessors.FujiProviaProcessor());
        filterProcessors.put(FilterType.FUJI_VELVIA, new FilterProcessors.FujiVelviaProcessor());
        filterProcessors.put(FilterType.FUJI_ASTIA, new FilterProcessors.FujiAstiaProcessor());
        filterProcessors.put(FilterType.FUJI_CLASSIC_CHROME, new FilterProcessors.FujiClassicChromeProcessor());
        filterProcessors.put(FilterType.FUJI_PRO_NEG_HI, new FilterProcessors.FujiProNegHiProcessor());
        filterProcessors.put(FilterType.FUJI_PRO_NEG_STD, new FilterProcessors.FujiProNegStdProcessor());
        filterProcessors.put(FilterType.FUJI_CLASSIC_NEG, new FilterProcessors.FujiClassicNegProcessor());
    }

    /**
     * 设置当前滤镜
     * @param filterType 滤镜类型
     */
    public void setCurrentFilter(FilterType filterType) {
        this.currentFilterType = filterType;
        this.currentFilterProcessor = filterProcessors.get(filterType);
    }

    /**
     * 获取当前滤镜类型
     * @return 当前滤镜类型
     */
    public FilterType getCurrentFilterType() {
        return currentFilterType;
    }

    /**
     * 获取当前滤镜处理器
     * @return 当前滤镜处理器
     */
    public FilterProcessor getCurrentFilterProcessor() {
        return currentFilterProcessor;
    }

    /**
     * 应用当前滤镜到位图
     * @param bitmap 原始位图
     * @return 应用滤镜后的位图
     */
    public Bitmap applyCurrentFilter(Bitmap bitmap) {
        if (currentFilterProcessor != null) {
            return currentFilterProcessor.applyFilter(bitmap);
        }
        return bitmap;
    }

    /**
     * 获取当前滤镜的颜色矩阵
     * @return ColorMatrix对象，如果当前滤镜不支持则返回null
     */
    public ColorMatrix getCurrentColorMatrix() {
        if (currentFilterProcessor != null) {
            return currentFilterProcessor.getColorMatrix();
        }
        return null;
    }

    /**
     * 获取当前滤镜的颜色矩阵滤镜
     * @return ColorMatrixColorFilter对象，如果当前滤镜不支持则返回null
     */
    public ColorMatrixColorFilter getCurrentColorMatrixFilter() {
        ColorMatrix matrix = getCurrentColorMatrix();
        if (matrix != null) {
            return new ColorMatrixColorFilter(matrix);
        }
        return null;
    }

    /**
     * 获取所有可用的滤镜类型
     * @return 滤镜类型列表
     */
    public List<FilterType> getAllFilterTypes() {
        return new ArrayList<>(filterProcessors.keySet());
    }

    /**
     * 获取指定类型的滤镜处理器
     * @param filterType 滤镜类型
     * @return 滤镜处理器
     */
    public FilterProcessor getFilterProcessor(FilterType filterType) {
        return filterProcessors.get(filterType);
    }
}
