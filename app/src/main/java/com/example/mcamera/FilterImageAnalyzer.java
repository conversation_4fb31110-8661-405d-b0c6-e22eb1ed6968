package com.example.mcamera;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.camera.core.ImageAnalysis;
import androidx.camera.core.ImageProxy;
import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;

/**
 * 滤镜图像分析器
 * 用于在相机预览中实时应用滤镜效果
 */
public class FilterImageAnalyzer implements ImageAnalysis.Analyzer {
    private static final String TAG = "FilterImageAnalyzer";
    private FilterManager filterManager;
    private OnFilteredImageListener listener;

    public interface OnFilteredImageListener {
        void onFilteredImage(Bitmap filteredBitmap);
    }

    public FilterImageAnalyzer(FilterManager filterManager) {
        this.filterManager = filterManager;
    }

    public void setOnFilteredImageListener(OnFilteredImageListener listener) {
        this.listener = listener;
    }

    @Override
    public void analyze(@NonNull ImageProxy image) {
        try {
            // 如果没有应用滤镜，直接返回
            if (filterManager.getCurrentFilterType() == FilterType.NONE) {
                image.close();
                return;
            }

            // 将ImageProxy转换为Bitmap
            Bitmap bitmap = imageProxyToBitmap(image);
            if (bitmap != null) {
                // 应用滤镜
                Bitmap filteredBitmap = filterManager.applyCurrentFilter(bitmap);
                if (filteredBitmap != null && listener != null) {
                    listener.onFilteredImage(filteredBitmap);
                }
                
                // 回收原始bitmap
                if (bitmap != filteredBitmap) {
                    bitmap.recycle();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error analyzing image: " + e.getMessage(), e);
        } finally {
            image.close();
        }
    }

    /**
     * 将ImageProxy转换为Bitmap
     * 注意：这是一个简化的实现，实际项目中可能需要更复杂的转换逻辑
     */
    private Bitmap imageProxyToBitmap(ImageProxy image) {
        try {
            // 获取图像的YUV数据
            ImageProxy.PlaneProxy[] planes = image.getPlanes();
            ByteBuffer yBuffer = planes[0].getBuffer();
            ByteBuffer uBuffer = planes[1].getBuffer();
            ByteBuffer vBuffer = planes[2].getBuffer();

            int ySize = yBuffer.remaining();
            int uSize = uBuffer.remaining();
            int vSize = vBuffer.remaining();

            byte[] nv21 = new byte[ySize + uSize + vSize];
            yBuffer.get(nv21, 0, ySize);
            vBuffer.get(nv21, ySize, vSize);
            uBuffer.get(nv21, ySize + vSize, uSize);

            // 创建YuvImage
            YuvImage yuvImage = new YuvImage(nv21, android.graphics.ImageFormat.NV21, 
                    image.getWidth(), image.getHeight(), null);
            
            // 转换为JPEG字节数组
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            yuvImage.compressToJpeg(new Rect(0, 0, image.getWidth(), image.getHeight()), 100, out);
            byte[] imageBytes = out.toByteArray();
            
            // 解码为Bitmap
            return android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
        } catch (Exception e) {
            Log.e(TAG, "Error converting ImageProxy to Bitmap: " + e.getMessage(), e);
            return null;
        }
    }
}
