package com.example.mcamera;

import android.graphics.ColorMatrix;

/**
 * 具体滤镜处理器实现类集合
 */
public class FilterProcessors {

    /**
     * 无滤镜处理器
     */
    public static class NoneFilterProcessor extends BaseFilterProcessor {
        public NoneFilterProcessor() {
            super(FilterType.NONE);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            return null; // 无滤镜不需要颜色矩阵
        }
    }

    /**
     * 黑白滤镜处理器
     */
    public static class BlackWhiteFilterProcessor extends BaseFilterProcessor {
        public BlackWhiteFilterProcessor() {
            super(FilterType.BLACK_WHITE);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix();
            matrix.setSaturation(0); // 设置饱和度为0，实现黑白效果
            return matrix;
        }
    }

    /**
     * 复古滤镜处理器
     */
    public static class VintageFilterProcessor extends BaseFilterProcessor {
        public VintageFilterProcessor() {
            super(FilterType.VINTAGE);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix(new float[]{
                    0.393f, 0.769f, 0.189f, 0, 0,
                    0.349f, 0.686f, 0.168f, 0, 0,
                    0.272f, 0.534f, 0.131f, 0, 0,
                    0, 0, 0, 1, 0
            });
            return matrix;
        }
    }

    /**
     * 棕褐色滤镜处理器
     */
    public static class SepiaFilterProcessor extends BaseFilterProcessor {
        public SepiaFilterProcessor() {
            super(FilterType.SEPIA);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix(new float[]{
                    0.393f, 0.769f, 0.189f, 0, 0,
                    0.349f, 0.686f, 0.168f, 0, 0,
                    0.272f, 0.534f, 0.131f, 0, 0,
                    0, 0, 0, 1, 0
            });
            return matrix;
        }
    }

    /**
     * 冷色调滤镜处理器
     */
    public static class CoolFilterProcessor extends BaseFilterProcessor {
        public CoolFilterProcessor() {
            super(FilterType.COOL);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix(new float[]{
                    0.8f, 0, 0, 0, 0,
                    0, 0.9f, 0, 0, 0,
                    0, 0, 1.2f, 0, 0,
                    0, 0, 0, 1, 0
            });
            return matrix;
        }
    }

    /**
     * 暖色调滤镜处理器
     */
    public static class WarmFilterProcessor extends BaseFilterProcessor {
        public WarmFilterProcessor() {
            super(FilterType.WARM);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix(new float[]{
                    1.2f, 0, 0, 0, 0,
                    0, 1.1f, 0, 0, 0,
                    0, 0, 0.8f, 0, 0,
                    0, 0, 0, 1, 0
            });
            return matrix;
        }
    }

    /**
     * 戏剧性滤镜处理器
     */
    public static class DramaticFilterProcessor extends BaseFilterProcessor {
        public DramaticFilterProcessor() {
            super(FilterType.DRAMATIC);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix(new float[]{
                    1.5f, 0, 0, 0, -20,
                    0, 1.5f, 0, 0, -20,
                    0, 0, 1.5f, 0, -20,
                    0, 0, 0, 1, 0
            });
            return matrix;
        }
    }

    /**
     * 鲜艳滤镜处理器
     */
    public static class VividFilterProcessor extends BaseFilterProcessor {
        public VividFilterProcessor() {
            super(FilterType.VIVID);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix();
            matrix.setSaturation(1.5f); // 增加饱和度
            return matrix;
        }
    }

    /**
     * 柔和滤镜处理器
     */
    public static class SoftFilterProcessor extends BaseFilterProcessor {
        public SoftFilterProcessor() {
            super(FilterType.SOFT);
        }

        @Override
        protected ColorMatrix createColorMatrix() {
            ColorMatrix matrix = new ColorMatrix(new float[]{
                    0.9f, 0.1f, 0.1f, 0, 10,
                    0.1f, 0.9f, 0.1f, 0, 10,
                    0.1f, 0.1f, 0.9f, 0, 10,
                    0, 0, 0, 1, 0
            });
            return matrix;
        }
    }

    // 富士胶片模拟滤镜处理器别名
    public static class FujiProviaProcessor extends FujiFilmSimulationProcessor.ProviaProcessor {}
    public static class FujiVelviaProcessor extends FujiFilmSimulationProcessor.VelviaProcessor {}
    public static class FujiAstiaProcessor extends FujiFilmSimulationProcessor.AstiaProcessor {}
    public static class FujiClassicChromeProcessor extends FujiFilmSimulationProcessor.ClassicChromeProcessor {}
    public static class FujiProNegHiProcessor extends FujiFilmSimulationProcessor.ProNegHiProcessor {}
    public static class FujiProNegStdProcessor extends FujiFilmSimulationProcessor.ProNegStdProcessor {}
    public static class FujiClassicNegProcessor extends FujiFilmSimulationProcessor.ClassicNegProcessor {}
}
