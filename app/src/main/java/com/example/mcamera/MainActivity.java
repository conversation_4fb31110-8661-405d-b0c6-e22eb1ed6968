// 定义包名
package com.example.mcamera;

// 导入Android相关的Manifest类，用于权限声明
import android.Manifest;
// 导入ContentValues类，用于存储键值对
import android.content.ContentValues;
// 导入PackageManager类，用于检查权限
import android.content.pm.PackageManager;
// 导入Build类，用于获取设备SDK版本
import android.os.Build;
// 导入Bundle类，用于在Activity之间传递数据
import android.os.Bundle;
// 导入MediaStore类，用于访问媒体存储
import android.provider.MediaStore;
// 导入Log类，用于日志输出
import android.util.Log;
// 导入ContentUris类，用于构建内容URI
import android.content.ContentUris;
// 导入Cursor类，用于数据库查询结果
import android.database.Cursor;
// 导入Uri类，用于表示资源的统一标识符
import android.net.Uri;
// 导入View类，表示用户界面组件
import android.view.View;
// 导入WindowInsets类，表示窗口的系统insets
import android.view.WindowInsets;
// 导入WindowInsetsController类，用于控制窗口insets的可见性
import android.view.WindowInsetsController;
// 导入ImageView类，用于显示图片
import android.widget.ImageView;
// 导入LinearLayout类，用于线性布局
import android.widget.LinearLayout;
// 导入TextView类，用于显示文本
import android.widget.TextView;
// 导入Toast类，用于显示短暂的提示信息
import android.widget.Toast;
// 导入Button类，用于创建按钮
import android.widget.Button;


// 导入NonNull注解，表示参数、字段或方法返回值不能为null
import androidx.annotation.NonNull;
// 导入AppCompatActivity，提供兼容性支持的Activity基类
import androidx.appcompat.app.AppCompatActivity;
// 导入CameraSelector类，用于选择相机
import androidx.camera.core.CameraSelector;
// 导入ImageCapture类，用于拍照
import androidx.camera.core.ImageCapture;
// 导入ImageCaptureException类，拍照异常
import androidx.camera.core.ImageCaptureException;
// 导入Preview类，用于相机预览
import androidx.camera.core.Preview;
// 导入ImageAnalysis类，用于图像分析
import androidx.camera.core.ImageAnalysis;
// 导入ProcessCameraProvider类，用于获取相机提供者实例
import androidx.camera.lifecycle.ProcessCameraProvider;
// 导入MediaStoreOutputOptions类，用于配置媒体存储输出
import androidx.camera.video.MediaStoreOutputOptions;
// 导入Quality类，用于设置视频质量
import androidx.camera.video.Quality;
// 导入QualitySelector类，用于选择视频质量
import androidx.camera.video.QualitySelector;
// 导入Recorder类，用于录制视频
import androidx.camera.video.Recorder;
// 导入Recording类，表示正在进行的视频录制
import androidx.camera.video.Recording;
// 导入VideoCapture类，用于视频拍摄
import androidx.camera.video.VideoCapture;
// 导入VideoRecordEvent类，视频录制事件
import androidx.camera.video.VideoRecordEvent;
// 导入ActivityCompat类，提供兼容性支持的权限请求
import androidx.core.app.ActivityCompat;
// 导入ContextCompat类，提供兼容性支持的上下文工具
import androidx.core.content.ContextCompat;
// 导入WindowCompat类，提供兼容性支持的窗口工具
import androidx.core.view.WindowCompat;
// 导入WindowInsetsCompat类，提供兼容性支持的窗口insets
import androidx.core.view.WindowInsetsCompat;
// 导入WindowInsetsControllerCompat类，提供兼容性支持的窗口insets控制器
import androidx.core.view.WindowInsetsControllerCompat;

// 导入NonNull注解，表示参数、字段或方法返回值不能为null
import androidx.annotation.NonNull;
// 导入AppCompatActivity，提供兼容性支持的Activity基类
import androidx.appcompat.app.AppCompatActivity;
// 导入CameraSelector类，用于选择相机
import androidx.camera.core.CameraSelector;
// 导入ImageCapture类，用于拍照
import androidx.camera.core.ImageCapture;
// 导入ImageCaptureException类，拍照异常
import androidx.camera.core.ImageCaptureException;
// 导入Preview类，用于相机预览
import androidx.camera.core.Preview;
// 导入ProcessCameraProvider类，用于获取相机提供者实例
import androidx.camera.lifecycle.ProcessCameraProvider;
// 导入MediaStoreOutputOptions类，用于配置媒体存储输出
import androidx.camera.video.MediaStoreOutputOptions;
// 导入Quality类，用于设置视频质量
import androidx.camera.video.Quality;
// 导入QualitySelector类，用于选择视频质量
import androidx.camera.video.QualitySelector;
// 导入Recorder类，用于录制视频
import androidx.camera.video.Recorder;
// 导入Recording类，表示正在进行的视频录制
import androidx.camera.video.Recording;
// 导入VideoCapture类，用于视频拍摄
import androidx.camera.video.VideoCapture;
// 导入VideoRecordEvent类，视频录制事件
import androidx.camera.video.VideoRecordEvent;
// 导入ActivityCompat类，提供兼容性支持的权限请求
import androidx.core.app.ActivityCompat;
// 导入ContextCompat类，提供兼容性支持的上下文工具
import androidx.core.content.ContextCompat;
// 导入WindowCompat类，提供兼容性支持的窗口工具
import androidx.core.view.WindowCompat;
// 导入WindowInsetsCompat类，提供兼容性支持的窗口insets
import androidx.core.view.WindowInsetsCompat;
// 导入WindowInsetsControllerCompat类，提供兼容性支持的窗口insets控制器
import androidx.core.view.WindowInsetsControllerCompat;

// 导入ActivityMainBinding类，由ViewBinding生成，用于访问布局视图
import com.example.mcamera.databinding.ActivityMainBinding;
// 导入ListenableFuture类，来自Guava库，表示一个可监听的异步操作
import com.google.common.util.concurrent.ListenableFuture;

// 导入RecyclerView相关类
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

// 导入CameraX Extensions相关类
import androidx.camera.extensions.ExtensionMode;
import androidx.camera.extensions.ExtensionsManager;
import com.google.common.util.concurrent.ListenableFuture;

// 导入SimpleDateFormat类，用于格式化日期
import java.text.SimpleDateFormat;
// 导入Locale类，用于指定语言环境
import java.util.Locale;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
// 导入ExecutorService类，用于管理线程池
import java.util.concurrent.ExecutorService;
// 导入Executors类，用于创建ExecutorService
import java.util.concurrent.Executors;
// 导入 Camera2CameraInfo 以获取 Camera2 ID
import androidx.camera.camera2.interop.Camera2CameraInfo;

import android.content.Context;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CameraCharacteristics;

// MainActivity类继承自AppCompatActivity，是应用的主Activity
public class MainActivity extends AppCompatActivity {

    // 定义日志标签
    private static final String TAG = "MCamera";
    // 定义权限请求码
    private static final int REQUEST_CODE_PERMISSIONS = 10;
    // 定义所需的权限数组，根据SDK版本动态调整
    private final String[] REQUIRED_PERMISSIONS =
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ? // 如果SDK版本大于等于TIRAMISU
                    new String[]{Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO} : // 则只需要相机和录音权限
                    new String[]{Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE}; // 否则还需要写入外部存储权限

    // ViewBinding实例，用于访问布局中的视图
    private ActivityMainBinding viewBinding;
    // ImageCapture实例，用于拍照用例
    private ImageCapture imageCapture;
    // VideoCapture实例，用于视频录制用例
    private VideoCapture<Recorder> videoCapture;
    // Recording实例，表示当前的视频录制
    private Recording recording;
    // 线程池，用于执行相机操作
    private ExecutorService cameraExecutor;

    // 相机镜头朝向，默认为后置 (This will be replaced by currentCameraId)
    // private int cameraLensFacing = CameraSelector.LENS_FACING_BACK;

    // CameraX 相机提供者
    private ProcessCameraProvider cameraProvider;
    // CameraX 相机控制器
    private androidx.camera.core.CameraControl cameraControl;
    // CameraX 相机信息
    private androidx.camera.core.CameraInfo cameraInfo;

    // 新增：后置摄像头按钮容器
    private LinearLayout rearCameraButtonsContainer;
    // 新增：存储所有相机ID到其CameraSelector的映射
    private Map<String, CameraSelector> allCameraSelectors = new HashMap<>();
    // 新增：存储所有后置摄像头ID的列表 (用于UI显示和逻辑判断)
    private List<String> rearCameraIds = new ArrayList<>();
    // 新增：当前激活的摄像头ID
    private String currentCameraId;
    // 新增：前置摄像头ID (用于逻辑判断)
    private String frontCameraId;

    // HDR状态，默认为禁用
    private boolean isHdrEnabled = false;
    // HDR扩展是否可用
    private boolean isHdrExtensionAvailable = false;
    // Extensions Manager
    private ExtensionsManager extensionsManager;

    // 滤镜相关成员变量
    private FilterManager filterManager;
    private RecyclerView filterSelector;
    private FilterAdapter filterAdapter;
    private FujiFilterAdapter fujiFilterAdapter;
    private boolean isFilterSelectorVisible = false;
    private boolean isFujiMode = false;
    private FilterImageAnalyzer filterImageAnalyzer;

    // 定义相机模式枚举
    private enum CameraMode {
        PHOTO, // 拍照模式
        VIDEO // 视频模式
    }

    // 当前相机模式，默认为拍照模式
    private CameraMode currentMode = CameraMode.PHOTO;

    // Activity创建时调用
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState); // 调用父类的onCreate方法

        // 通过ViewBinding Inflate布局
        viewBinding = ActivityMainBinding.inflate(getLayoutInflater());
        // 设置Activity的内容视图
        setContentView(viewBinding.getRoot());

        // 初始化后置摄像头按钮容器
        rearCameraButtonsContainer = viewBinding.rearCameraButtonsContainer;

        // 初始化滤镜功能
        initializeFilters();

        // 调试：显示相机信息
        debugCameraInfo();

        // 启用全屏沉浸模式
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false); // 设置窗口不适应系统insets
        WindowInsetsControllerCompat windowInsetsController =
                WindowCompat.getInsetsController(getWindow(), viewBinding.getRoot()); // 获取窗口insets控制器
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars()); // 隐藏系统栏
        windowInsetsController.setSystemBarsBehavior(
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE // 设置系统栏行为为滑动时显示
        );

        // 请求相机权限
        if (allPermissionsGranted()) { // 如果所有权限都已授予
            //enumerateCamerasAndSetupButtons();// will be called inside startCamera()
            startCamera(); // 启动相机
        } else { // 否则
            ActivityCompat.requestPermissions( // 请求权限
                    this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS);
        }

        // 设置拍照按钮和模式切换按钮的监听器
        viewBinding.cameraCaptureButton.setOnClickListener(v -> captureAction()); // 拍照/录像按钮点击事件
        viewBinding.cameraSwitchButton.setOnClickListener(v -> toggleCamera()); // 切换相机按钮点击事件

        // 模式选择监听器
        viewBinding.modePhoto.setOnClickListener(v -> setCameraMode(CameraMode.PHOTO)); // 拍照模式按钮点击事件
        viewBinding.modeVideo.setOnClickListener(v -> setCameraMode(CameraMode.VIDEO)); // 视频模式按钮点击事件

        // 缩放选择监听器 (Removed as zoom is controlled by SeekBar)
        // viewBinding.zoom06x.setOnClickListener(v -> setZoom(0.6f)); // 0.6x 缩放按钮点击事件
        // viewBinding.zoom1x.setOnClickListener(v -> setZoom(1.0f)); // 1x 缩放按钮点击事件
        // viewBinding.zoom2x.setOnClickListener(v -> setZoom(2.0f)); // 2x 缩放按钮点击事件
        // viewBinding.zoom32x.setOnClickListener(v -> setZoom(3.2f)); // 3.2x 缩放按钮点击事件

        // 顶部栏按钮监听器（目前只显示Toast）
        viewBinding.flashButton.setOnClickListener(v -> Toast.makeText(this, "Flash button clicked", Toast.LENGTH_SHORT).show()); // 闪光灯按钮点击事件
        viewBinding.hdrButton.setOnClickListener(v -> toggleHdr()); // HDR按钮点击事件
        viewBinding.filterButton.setOnClickListener(v -> toggleFilterSelector()); // 滤镜按钮点击事件
        viewBinding.filterButton.setOnLongClickListener(v -> {
            hideFilterSelector();
            return true;
        }); // 滤镜按钮长按隐藏
        viewBinding.leicaLogo.setOnClickListener(v -> Toast.makeText(this, "Leica logo clicked", Toast.LENGTH_SHORT).show()); // 徕卡Logo点击事件

        // 初始化相机执行器
        cameraExecutor = Executors.newSingleThreadExecutor();

        // 初始化模式设置
        setCameraMode(currentMode); // 根据当前模式设置相机
    }

    // 设置缩放级别 (This method is now redundant with the SeekBar)
    // private void setZoom(float zoomRatio) {
    //     if (cameraControl != null) {
    //         cameraControl.setZoomRatio(zoomRatio);
    //         Log.d(TAG, "Setting zoom ratio to: " + zoomRatio);
    //     } else {
    //         Log.w(TAG, "CameraControl is not available for setting zoom.");
    //     }
    // }

    // 捕获动作，根据当前模式执行拍照或录像
    private void captureAction() {
        if (currentMode == CameraMode.PHOTO) { // 如果是拍照模式
            takePhoto(); // 执行拍照
        } else if (currentMode == CameraMode.VIDEO) { // 如果是视频模式
            captureVideo(); // 执行录像
        } else { // 其他情况
            Toast.makeText(this, "Please select Photo or Video mode for capture.", Toast.LENGTH_SHORT).show(); // 提示选择模式
        }
    }

    // 执行拍照
    private void takePhoto() {
        if (imageCapture == null) { // 如果ImageCapture未初始化
            return; // 返回
        }

        // 生成文件名
        String name = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
                .format(System.currentTimeMillis()); // 使用当前时间戳格式化文件名
        // 创建ContentValues用于存储媒体信息
        ContentValues contentValues = new ContentValues();
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, name); // 设置显示名称
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg"); // 设置MIME类型为JPEG图片
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) { // 如果SDK版本大于Android P
            contentValues.put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/MCamera-Images"); // 设置相对路径
        }

        // 构建OutputFileOptions
        ImageCapture.OutputFileOptions outputOptions = new ImageCapture.OutputFileOptions.Builder(
                getContentResolver(), // 内容解析器
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI, // 外部图片URI
                contentValues) // 内容值
                .build(); // 构建

        // 执行拍照
        imageCapture.takePicture(
                outputOptions, // 输出选项
                ContextCompat.getMainExecutor(this), // 主线程执行器
                new ImageCapture.OnImageSavedCallback() { // 图片保存回调
                    @Override
                    public void onImageSaved(@NonNull ImageCapture.OutputFileResults outputFileResults) { // 图片保存成功
                        String msg = "Photo capture succeeded: " + outputFileResults.getSavedUri(); // 成功消息
                        Toast.makeText(getBaseContext(), msg, Toast.LENGTH_SHORT).show(); // 显示Toast
                        Log.d(TAG, msg); // 打印日志
                        loadLatestImageThumbnail(); // 更新图库缩略图
                    }

                    @Override
                    public void onError(@NonNull ImageCaptureException exc) { // 图片保存失败
                        Log.e(TAG, "Photo capture failed: " + exc.getMessage(), exc); // 打印错误日志
                        Toast.makeText(getBaseContext(), "Photo capture failed: " + exc.getMessage(), Toast.LENGTH_SHORT).show(); // 显示Toast
                    }
                }
        );
    }

    // 执行视频录制
    private void captureVideo() {
        if (videoCapture == null) { // 如果VideoCapture未初始化
            return; // 返回
        }

        viewBinding.cameraCaptureButton.setEnabled(false); // 禁用拍照按钮

        if (recording != null) { // 如果正在录制
            recording.stop(); // 停止录制
            recording = null; // 清空recording实例
            return; // 返回
        }

        // 生成文件名
        String name = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
                .format(System.currentTimeMillis()); // 使用当前时间戳格式化文件名
        // 创建ContentValues用于存储媒体信息
        ContentValues contentValues = new ContentValues();
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, name); // 设置显示名称
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "video/mp4"); // 设置MIME类型为MP4视频
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) { // 如果SDK版本大于Android P
            contentValues.put(MediaStore.Video.Media.RELATIVE_PATH, "Movies/MCamera-Videos"); // 设置相对路径
        }

        // 构建MediaStoreOutputOptions
        MediaStoreOutputOptions mediaStoreOutputOptions = new MediaStoreOutputOptions.Builder(
                getContentResolver(), // 内容解析器
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI) // 外部视频URI
                .setContentValues(contentValues) // 内容值
                .build(); // 构建

        // 检查录音权限
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(this, "RECORD_AUDIO permission not granted.", Toast.LENGTH_SHORT).show(); // 提示录音权限未授予
            return; // 返回
        }
        // 准备并开始录制
        recording = videoCapture.getOutput().prepareRecording(this, mediaStoreOutputOptions)
                .withAudioEnabled() // 启用音频
                .start(ContextCompat.getMainExecutor(this), videoRecordEvent -> { // 开始录制并设置事件监听器
                    if (videoRecordEvent instanceof VideoRecordEvent.Start) { // 如果是录制开始事件
                        viewBinding.cameraCaptureButton.setBackgroundTintList(ContextCompat.getColorStateList(MainActivity.this, android.R.color.holo_red_light)); // 设置按钮背景为红色
                        viewBinding.cameraCaptureButton.setImageResource(0); // 清除按钮图片
                        viewBinding.cameraCaptureButton.setEnabled(true); // 启用按钮
                    } else if (videoRecordEvent instanceof VideoRecordEvent.Finalize) { // 如果是录制结束事件
                        VideoRecordEvent.Finalize finalizeEvent = (VideoRecordEvent.Finalize) videoRecordEvent; // 转换为Finalize事件
                        if (!finalizeEvent.hasError()) { // 如果没有错误
                            String msg = "Video capture succeeded: " + finalizeEvent.getOutputResults().getOutputUri(); // 成功消息
                            Toast.makeText(getBaseContext(), msg, Toast.LENGTH_SHORT).show(); // 显示Toast
                            Log.d(TAG, msg); // 打印日志
                            loadLatestImageThumbnail(); // 更新图库缩略图
                        } else { // 如果有错误
                            recording.close(); // 关闭录制
                            recording = null; // 清空recording实例
                            String msg = "Video capture failed: " + finalizeEvent.getError(); // 错误消息
                            Toast.makeText(getBaseContext(), msg, Toast.LENGTH_SHORT).show(); // 显示Toast
                            Log.e(TAG, msg); // 打印错误日志
                        }
                        viewBinding.cameraCaptureButton.setBackgroundTintList(ContextCompat.getColorStateList(MainActivity.this, android.R.color.white)); // 设置按钮背景为白色
                        viewBinding.cameraCaptureButton.setImageResource(android.R.drawable.ic_menu_camera); // 设置按钮图片为相机图标
                        viewBinding.cameraCaptureButton.setEnabled(true); // 启用按钮
                    }
                });
    }

    // 启动相机
    private void startCamera() {
        // 获取ProcessCameraProvider的ListenableFuture实例
        ListenableFuture<ProcessCameraProvider> cameraProviderFuture = ProcessCameraProvider.getInstance(this);

        // 添加监听器，当cameraProviderFuture完成时执行
        cameraProviderFuture.addListener(() -> {
            try {
                // 获取ProcessCameraProvider实例
                cameraProvider = cameraProviderFuture.get();

                // 初始化Extensions Manager
                initializeExtensionsManager();

                enumerateCamerasAndSetupButtons();
                // 构建Preview用例
                Preview preview = new Preview.Builder().build();
                // 设置预览的SurfaceProvider
                preview.setSurfaceProvider(viewBinding.viewFinder.getSurfaceProvider());

                // 应用滤镜到预览（如果有的话）
                applyFilterToPreview();

                // 构建ImageCapture用例
                ImageCapture.Builder imageCaptureBuilder = new ImageCapture.Builder()
                        .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY); // 设置捕获模式为最小延迟

                imageCapture = imageCaptureBuilder.build(); // 构建

                // 构建Recorder用例
                Recorder recorder = new Recorder.Builder()
                        .setQualitySelector(QualitySelector.from(Quality.HIGHEST)) // 设置视频质量为最高
                        .build(); // 构建
                // 使用Recorder构建VideoCapture用例
                videoCapture = VideoCapture.withOutput(recorder);

                // 构建ImageAnalysis用例用于滤镜处理
                ImageAnalysis imageAnalysis = new ImageAnalysis.Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build();

                // 设置滤镜分析器
                if (filterImageAnalyzer == null) {
                    filterImageAnalyzer = new FilterImageAnalyzer(filterManager);
                }
                imageAnalysis.setAnalyzer(cameraExecutor, filterImageAnalyzer);

                // 构建CameraSelector，选择当前激活的摄像头
                CameraSelector cameraSelector = allCameraSelectors.get(currentCameraId);
                if (cameraSelector == null) {
                    Log.e(TAG, "No CameraSelector found for currentCameraId: " + currentCameraId);
                    // Fallback to default if currentCameraId is invalid
                    if (!rearCameraIds.isEmpty()) {
                        currentCameraId = rearCameraIds.get(0);
                        cameraSelector = allCameraSelectors.get(currentCameraId);
                    } else if (frontCameraId != null) {
                        currentCameraId = frontCameraId;
                        cameraSelector = allCameraSelectors.get(currentCameraId);
                    } else {
                        Toast.makeText(MainActivity.this, "No cameras available.", Toast.LENGTH_LONG).show();
                        return;
                    }
                }

                // 解绑所有用例
                cameraProvider.unbindAll();

                // 绑定相机用例，根据HDR状态选择不同的绑定方式
                androidx.camera.core.Camera camera;
                if (isHdrEnabled && isHdrExtensionAvailable && extensionsManager != null) {
                    // 使用HDR扩展绑定
                    camera = bindCameraWithHdrExtension(cameraSelector, preview, imageCapture, videoCapture, imageAnalysis);
                } else {
                    // 普通绑定
                    camera = cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture, videoCapture, imageAnalysis);
                }

                // Get CameraControl and CameraInfo
                cameraControl = camera.getCameraControl();
                cameraInfo = camera.getCameraInfo();

                // Set up zoom seek bar
                cameraInfo.getZoomState().observe(this, zoomState -> {
                    float minZoomRatio = zoomState.getMinZoomRatio();
                    float maxZoomRatio = zoomState.getMaxZoomRatio();
                    float currentZoomRatio = zoomState.getZoomRatio();

                    // Map zoom ratio to SeekBar progress (e.g., 0 to 100)
                    int maxProgress = 100;
                    viewBinding.zoomSeekBar.setMax(maxProgress);
                    int currentProgress = (int) ((currentZoomRatio - minZoomRatio) / (maxZoomRatio - minZoomRatio) * maxProgress);
                    viewBinding.zoomSeekBar.setProgress(currentProgress);
                    viewBinding.zoomLevelTextView.setText(String.format(Locale.US, "%.1fx", currentZoomRatio));

                    // Disable zoom buttons and show seek bar
                    viewBinding.zoomSelector.setVisibility(View.GONE);
                    viewBinding.zoomSeekBar.setVisibility(View.VISIBLE);
                    viewBinding.zoomLevelTextView.setVisibility(View.VISIBLE);
                });

                viewBinding.zoomSeekBar.setOnSeekBarChangeListener(new android.widget.SeekBar.OnSeekBarChangeListener() {
                    @Override
                    public void onProgressChanged(android.widget.SeekBar seekBar, int progress, boolean fromUser) {
                        if (cameraControl != null && cameraInfo != null && cameraInfo.getZoomState().getValue() != null) {
                            float minZoomRatio = cameraInfo.getZoomState().getValue().getMinZoomRatio();
                            float maxZoomRatio = cameraInfo.getZoomState().getValue().getMaxZoomRatio();
                            float zoomRatio = minZoomRatio + (maxZoomRatio - minZoomRatio) * (progress / (float) seekBar.getMax());
                            cameraControl.setZoomRatio(zoomRatio);
                            viewBinding.zoomLevelTextView.setText(String.format(Locale.US, "%.1fx", zoomRatio));
                            Log.d(TAG, "SeekBar progress: " + progress + ", setting zoom ratio to: " + zoomRatio);
                        }
                    }

                    @Override
                    public void onStartTrackingTouch(android.widget.SeekBar seekBar) {
                        // Not needed for this implementation
                    }

                    @Override
                    public void onStopTrackingTouch(android.widget.SeekBar seekBar) {
                        // Not needed for this implementation
                    }
                });


                // Add touch listener for manual focus
                viewBinding.viewFinder.setOnTouchListener((view, event) -> {
                    switch (event.getAction()) {
                        case android.view.MotionEvent.ACTION_DOWN:
                            Log.d(TAG, "ACTION_DOWN at " + event.getX() + ", " + event.getY());
                            break;
                        case android.view.MotionEvent.ACTION_MOVE:
                            Log.d(TAG, "ACTION_MOVE at " + event.getX() + ", " + event.getY());
                            break;
                        case android.view.MotionEvent.ACTION_UP:
                            Log.d(TAG, "ACTION_UP at " + event.getX() + ", " + event.getY());
                            float x = event.getX();
                            float y = event.getY();

                            // Create a MeteringPointFactory
                            androidx.camera.core.MeteringPointFactory factory = new androidx.camera.core.SurfaceOrientedMeteringPointFactory(
                                    viewBinding.viewFinder.getWidth(), viewBinding.viewFinder.getHeight());

                        // Create a MeteringPoint
                        androidx.camera.core.MeteringPoint meteringPoint = factory.createPoint(x, y);

                        // Create a FocusMeteringAction
                        androidx.camera.core.FocusMeteringAction action = new androidx.camera.core.FocusMeteringAction.Builder(meteringPoint)
                                .addPoint(meteringPoint, androidx.camera.core.FocusMeteringAction.FLAG_AF | androidx.camera.core.FocusMeteringAction.FLAG_AE) // Add auto-focus and auto-exposure point
                                .build();

                        // Check if focus and metering is supported at the metering point
                        if (cameraInfo.isFocusMeteringSupported(action)) {
                            // Start the focus and metering action
                            ListenableFuture<androidx.camera.core.FocusMeteringResult> future = cameraControl.startFocusAndMetering(action);
                            future.addListener(() -> {
                                try {
                                    androidx.camera.core.FocusMeteringResult result = future.get();
                                    if (result.isFocusSuccessful()) {
                                        Log.d(TAG, "Manual focus succeeded");
                                    } else {
                                        Log.d(TAG, "Manual focus failed");
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "Manual focus error: " + e.getMessage());
                                }
                            }, ContextCompat.getMainExecutor(MainActivity.this));
                        }
                            return true; // Consume the event
                    }
                    return true; // Consume the event for ACTION_DOWN and ACTION_MOVE as well
                });


            } catch (Exception exc) { // 捕获异常
                Log.e(TAG, "Use case binding failed", exc); // 打印错误日志
                Toast.makeText(this, "Camera initialization failed: " + exc.getMessage(), Toast.LENGTH_SHORT).show(); // 显示Toast
            }
        }, ContextCompat.getMainExecutor(this)); // 在主线程执行监听器

        // Update visibility of rear camera buttons based on current camera
        updateRearCameraButtonVisibility();
    }

    // 新增：切换相机（前置/后置/多个后置）
    private void toggleCamera() {
        if (currentCameraId == null || allCameraSelectors.isEmpty()) {
            Toast.makeText(this, "No camera available to switch.", Toast.LENGTH_SHORT).show();
            return;
        }

        // Determine if current camera is front or rear based on its CameraSelector
        boolean isCurrentCameraFront = false;
        CameraSelector currentSelector = allCameraSelectors.get(currentCameraId);
        if (currentSelector != null) {
            try {
                // Get the CameraInfo associated with the current CameraSelector
                // This is more direct as allCameraSelectors now maps to unique CameraSelectors
                // that were built from specific CameraInfo objects.
                // We need to find the CameraInfo that matches the currentSelector.
                List<androidx.camera.core.CameraInfo> availableCameraInfos = cameraProvider.getAvailableCameraInfos();
                for (androidx.camera.core.CameraInfo info : availableCameraInfos) {
                    // Check if this CameraInfo is selected by the currentSelector
                    // The selector was built with cameraInfo::equals, so this should work.
                    List<androidx.camera.core.CameraInfo> filteredByCurrentSelector = currentSelector.filter(java.util.Collections.singletonList(info));
                    if (!filteredByCurrentSelector.isEmpty() && filteredByCurrentSelector.get(0).equals(info)) {
                        Integer lensFacing = info.getLensFacing();
                        if (lensFacing != null && lensFacing.equals(CameraSelector.LENS_FACING_FRONT)) {
                            isCurrentCameraFront = true;
                        }
                        break;
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error determining camera lens facing: " + e.getMessage());
                Toast.makeText(this, "Error switching camera.", Toast.LENGTH_SHORT).show();
                return;
            }
        }


        if (isCurrentCameraFront) {
            // If currently on front camera, switch to the first rear camera
            if (!rearCameraIds.isEmpty()) {
                switchCameraById(rearCameraIds.get(0));
                Toast.makeText(this, "Switched to Rear Camera 1", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "No rear cameras available.", Toast.LENGTH_SHORT).show();
            }
        } else {
            // If currently on a rear camera, find its index and switch to the next rear camera
            // If it's the last rear camera, switch to the front camera
            int currentIndex = rearCameraIds.indexOf(currentCameraId);
            if (currentIndex != -1 && currentIndex < rearCameraIds.size() - 1) {
                // Switch to next rear camera
                switchCameraById(rearCameraIds.get(currentIndex + 1));
                Toast.makeText(this, "Switched to Rear Camera " + (currentIndex + 2), Toast.LENGTH_SHORT).show();
            } else {
                // Switch to front camera
                if (frontCameraId != null) {
                    switchCameraById(frontCameraId);
                    Toast.makeText(this, "Switched to Front Camera", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "No front camera available.", Toast.LENGTH_SHORT).show();
                }
            }
        }
    }

    // 新增：根据ID切换相机
    private void switchCameraById(String cameraId) {
        if (cameraId == null || cameraId.equals(currentCameraId)) {
            Log.d(TAG, "Camera switch ignored: same camera or null ID");
            return;
        }

        if (!allCameraSelectors.containsKey(cameraId)) {
            Log.e(TAG, "Camera ID not found: " + cameraId);
            Toast.makeText(this, "Camera not available", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Switching camera from " + currentCameraId + " to " + cameraId);
        String previousCameraId = currentCameraId;
        currentCameraId = cameraId;

        try {
            // 重新检查HDR可用性（不同相机可能有不同的扩展支持）
            checkHdrExtensionAvailability();
            startCamera();
            updateRearCameraButtonStates(); // Update button highlights

            Log.d(TAG, "Camera switch successful: " + cameraId);
        } catch (Exception e) {
            Log.e(TAG, "Camera switch failed: " + e.getMessage());
            // 回退到之前的相机
            currentCameraId = previousCameraId;
            Toast.makeText(this, "Failed to switch camera", Toast.LENGTH_SHORT).show();
        }
    }

    // 切换HDR状态
    private void toggleHdr() {
        if (!isHdrExtensionAvailable) {
            Toast.makeText(this, "HDR not supported on this camera", Toast.LENGTH_SHORT).show();
            return;
        }

        isHdrEnabled = !isHdrEnabled;
        updateHdrButtonState();
        startCamera(); // Rebind camera use cases to reflect HDR state

        String message = isHdrEnabled ? "HDR Enabled" : "HDR Disabled";
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        Log.d(TAG, message);
    }

    // 更新HDR按钮状态
    private void updateHdrButtonState() {
        if (isHdrEnabled && isHdrExtensionAvailable) {
            viewBinding.hdrButton.setColorFilter(ContextCompat.getColor(this, android.R.color.holo_red_light)); // Button turns red
        } else {
            viewBinding.hdrButton.setColorFilter(ContextCompat.getColor(this, android.R.color.white)); // Button turns white
        }
    }

    // 更新HDR按钮可用性
    private void updateHdrButtonAvailability() {
        viewBinding.hdrButton.setEnabled(isHdrExtensionAvailable);
        if (!isHdrExtensionAvailable) {
            viewBinding.hdrButton.setAlpha(0.5f); // 半透明表示不可用
            isHdrEnabled = false; // 强制禁用HDR
        } else {
            viewBinding.hdrButton.setAlpha(1.0f); // 完全不透明表示可用
        }
        updateHdrButtonState();
    }

    // Setting camera mode
    private void setCameraMode(CameraMode mode) {
        currentMode = mode; // 更新当前模式
        startCamera(); // 重新启动相机以应用新的模式

        // 重置所有模式文本颜色并隐藏所有指示器
        viewBinding.modeVideo.setTextColor(ContextCompat.getColor(this, android.R.color.white)); // 视频模式文本颜色设为白色
        viewBinding.indicatorVideo.setVisibility(View.INVISIBLE); // 视频模式指示器隐藏
        viewBinding.modePhoto.setTextColor(ContextCompat.getColor(this, android.R.color.white)); // 拍照模式文本颜色设为白色
        viewBinding.indicatorPhoto.setVisibility(View.INVISIBLE); // 拍照模式指示器隐藏

        // 突出显示选定的模式并显示其指示器
        switch (mode) {
            case PHOTO: // 如果是拍照模式
                viewBinding.modePhoto.setTextColor(ContextCompat.getColor(this, android.R.color.holo_red_light)); // 拍照模式文本颜色设为红色
                viewBinding.indicatorPhoto.setVisibility(View.VISIBLE); // 拍照模式指示器显示
                viewBinding.cameraCaptureButton.setImageResource(android.R.drawable.ic_menu_camera); // 设置拍照按钮图片为相机图标
                viewBinding.cameraCaptureButton.setBackgroundTintList(ContextCompat.getColorStateList(this, android.R.color.white)); // 设置拍照按钮背景为白色
                break;
            case VIDEO: // 如果是视频模式
                viewBinding.modeVideo.setTextColor(ContextCompat.getColor(this, android.R.color.holo_red_light)); // 视频模式文本颜色设为红色
                viewBinding.indicatorVideo.setVisibility(View.VISIBLE); // 视频模式指示器显示
                viewBinding.cameraCaptureButton.setImageResource(0); // 清除拍照按钮图片
                viewBinding.cameraCaptureButton.setBackgroundTintList(ContextCompat.getColorStateList(this, android.R.color.white)); // 设置拍照按钮背景为白色
                break;
            default: // 默认情况（不应该发生）
                // 任何其他可能被选择的模式的备用方案
                viewBinding.cameraCaptureButton.setImageResource(android.R.drawable.ic_menu_camera); // 设置拍照按钮图片为相机图标
                viewBinding.cameraCaptureButton.setBackgroundTintList(ContextCompat.getColorStateList(this, android.R.color.white)); // 设置拍照按钮背景为白色
                break;
        }
    }

    // 权限请求结果回调
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults); // 调用父类方法
        if (requestCode == REQUEST_CODE_PERMISSIONS) { // 如果是相机权限请求
            if (allPermissionsGranted()) { // 如果所有权限都已授予
                startCamera(); // 启动相机
                loadLatestImageThumbnail(); // 权限授予后加载最新图片缩略图
            } else { // 否则
                Toast.makeText(this, // 显示Toast
                        "Permissions not granted by the user.",
                        Toast.LENGTH_SHORT).show();
                finish(); // 结束Activity
            }
        }
    }

    // 新增：枚举相机并设置按钮 (现在完全基于 CameraX API)
    private void enumerateCamerasAndSetupButtons() {
        allCameraSelectors.clear();
        rearCameraIds.clear();
        rearCameraButtonsContainer.removeAllViews(); // Clear existing buttons

        try {
            List<androidx.camera.core.CameraInfo> availableCameraInfos = cameraProvider.getAvailableCameraInfos();
            Log.d(TAG, "Found " + availableCameraInfos.size() + " cameras");

            // 使用Camera2 API来获取更详细的相机信息
            CameraManager cameraManager = (CameraManager) getSystemService(Context.CAMERA_SERVICE);
            String[] cameraIds = cameraManager.getCameraIdList();

            int rearCameraCount = 0;

            // 遍历所有Camera2 ID来确保我们获取所有相机
            for (String camera2Id : cameraIds) {
                try {
                    CameraCharacteristics characteristics = cameraManager.getCameraCharacteristics(camera2Id);
                    Integer lensFacing = characteristics.get(CameraCharacteristics.LENS_FACING);

                    if (lensFacing != null) {
                        Log.d(TAG, "Camera " + camera2Id + " facing: " +
                            (lensFacing == CameraCharacteristics.LENS_FACING_BACK ? "BACK" :
                             lensFacing == CameraCharacteristics.LENS_FACING_FRONT ? "FRONT" : "EXTERNAL"));

                        // 找到对应的CameraInfo
                        androidx.camera.core.CameraInfo matchingCameraInfo = null;
                        for (androidx.camera.core.CameraInfo cameraInfo : availableCameraInfos) {
                            try {
                                String cameraXId = androidx.camera.camera2.interop.Camera2CameraInfo.from(cameraInfo).getCameraId();
                                if (cameraXId.equals(camera2Id)) {
                                    matchingCameraInfo = cameraInfo;
                                    break;
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error getting CameraX ID: " + e.getMessage());
                            }
                        }

                        if (matchingCameraInfo != null) {
                            // 创建CameraSelector
                            CameraSelector selector = new CameraSelector.Builder()
                                    .addCameraFilter(infos -> {
                                        List<androidx.camera.core.CameraInfo> filteredList = new ArrayList<>();
                                        for (androidx.camera.core.CameraInfo info : infos) {
                                            try {
                                                String id = androidx.camera.camera2.interop.Camera2CameraInfo.from(info).getCameraId();
                                                if (id.equals(camera2Id)) {
                                                    filteredList.add(info);
                                                }
                                            } catch (Exception e) {
                                                Log.e(TAG, "Error filtering camera by ID: " + e.getMessage());
                                            }
                                        }
                                        return filteredList;
                                    })
                                    .build();

                            if (lensFacing == CameraCharacteristics.LENS_FACING_BACK) {
                                rearCameraCount++;
                                rearCameraIds.add(camera2Id);
                                allCameraSelectors.put(camera2Id, selector);

                                // 获取相机的焦距信息来生成更好的按钮名称
                                String buttonText = getCameraButtonText(characteristics, rearCameraCount);

                                // 创建按钮
                                createRearCameraButton(camera2Id, buttonText);

                                Log.d(TAG, "Added rear camera: " + camera2Id + " (" + buttonText + ")");

                            } else if (lensFacing == CameraCharacteristics.LENS_FACING_FRONT) {
                                frontCameraId = camera2Id;
                                allCameraSelectors.put(camera2Id, selector);
                                Log.d(TAG, "Added front camera: " + camera2Id);
                            }
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing camera " + camera2Id + ": " + e.getMessage());
                }
            }

            Log.d(TAG, "Found " + rearCameraIds.size() + " rear cameras and " +
                (frontCameraId != null ? "1" : "0") + " front camera");

            // Set initial camera if not already set
            if (currentCameraId == null) {
                if (!rearCameraIds.isEmpty()) {
                    currentCameraId = rearCameraIds.get(0); // Default to first rear camera
                    Log.d(TAG, "Set initial camera to: " + currentCameraId);
                } else if (frontCameraId != null) {
                    currentCameraId = frontCameraId; // Fallback to front camera if no rear
                    Log.d(TAG, "Set initial camera to front: " + currentCameraId);
                } else {
                    Toast.makeText(this, "No cameras found on this device.", Toast.LENGTH_LONG).show();
                }
            }
            updateRearCameraButtonStates(); // Update button highlights and visibility
        } catch (Exception e) { // Catch generic Exception for CameraX related issues
            Log.e(TAG, "Error enumerating cameras: " + e.getMessage());
            Toast.makeText(this, "Error accessing camera information.", Toast.LENGTH_SHORT).show();
        }
    }

    // 获取相机按钮文本
    private String getCameraButtonText(CameraCharacteristics characteristics, int cameraIndex) {
        try {
            // 尝试获取焦距信息
            float[] focalLengths = characteristics.get(CameraCharacteristics.LENS_INFO_AVAILABLE_FOCAL_LENGTHS);
            if (focalLengths != null && focalLengths.length > 0) {
                float focalLength = focalLengths[0];
                // 根据焦距推断相机类型
                if (focalLength < 3.0f) {
                    return "Ultra"; // 超广角
                } else if (focalLength < 6.0f) {
                    return "Wide"; // 广角/主摄
                } else if (focalLength < 10.0f) {
                    return "Tele"; // 长焦
                } else {
                    return "Zoom"; // 变焦
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting focal length: " + e.getMessage());
        }

        // 回退到简单的编号
        return "Rear " + cameraIndex;
    }

    // 创建后置相机按钮
    private void createRearCameraButton(String cameraId, String buttonText) {
        Button cameraButton = new Button(this);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(8, 0, 8, 0);
        cameraButton.setLayoutParams(params);
        cameraButton.setText(buttonText);
        cameraButton.setTextColor(ContextCompat.getColor(this, android.R.color.white));
        cameraButton.setBackgroundColor(ContextCompat.getColor(this, android.R.color.transparent));
        cameraButton.setTag(cameraId);
        cameraButton.setMinWidth(0);
        cameraButton.setMinimumWidth(0);
        cameraButton.setPadding(16, 8, 16, 8);

        cameraButton.setOnClickListener(v -> {
            String id = (String) v.getTag();
            Log.d(TAG, "Camera button clicked: " + id + " (" + buttonText + ")");
            switchCameraById(id);
        });

        rearCameraButtonsContainer.addView(cameraButton);
    }

    // 调试相机信息
    private void debugCameraInfo() {
        try {
            CameraManager manager = (CameraManager) getSystemService(Context.CAMERA_SERVICE);
            String[] cameraIds = manager.getCameraIdList();

            Log.d(TAG, "=== 相机调试信息 ===");
            Log.d(TAG, "总共发现 " + cameraIds.length + " 个摄像头");
            Log.d(TAG, "后置摄像头数量: " + rearCameraIds.size());
            Log.d(TAG, "前置摄像头数量: " + frontCameraIds.size());
            Log.d(TAG, "当前摄像头ID: " + currentCameraId);

            for (String cameraId : cameraIds) {
                CameraCharacteristics characteristics = manager.getCameraCharacteristics(cameraId);
                Integer facing = characteristics.get(CameraCharacteristics.LENS_FACING);
                String facingStr = facing == CameraCharacteristics.LENS_FACING_BACK ? "后置" :
                                 facing == CameraCharacteristics.LENS_FACING_FRONT ? "前置" : "其他";
                Log.d(TAG, "摄像头 " + cameraId + ": " + facingStr);
            }

            // 强制显示按钮容器用于调试
            if (rearCameraButtonsContainer != null) {
                rearCameraButtonsContainer.setVisibility(View.VISIBLE);
                Log.d(TAG, "强制显示后置摄像头按钮容器");
            }

        } catch (Exception e) {
            Log.e(TAG, "调试相机信息时出错", e);
        }
    }

    // 新增：更新后置摄像头按钮状态和容器可见性
    private void updateRearCameraButtonStates() {
        boolean isCurrentCameraRear = rearCameraIds.contains(currentCameraId);

        Log.d(TAG, "Updating rear camera button states. Current camera: " + currentCameraId +
            ", is rear: " + isCurrentCameraRear + ", rear cameras: " + rearCameraIds.size());

        if (isCurrentCameraRear && rearCameraIds.size() >= 1) {
            // 显示后置摄像头按钮（即使只有一个也显示，便于调试）
            rearCameraButtonsContainer.setVisibility(View.VISIBLE);
            Log.d(TAG, "显示后置摄像头按钮容器");

            for (int i = 0; i < rearCameraButtonsContainer.getChildCount(); i++) {
                View child = rearCameraButtonsContainer.getChildAt(i);
                if (child instanceof Button) {
                    Button button = (Button) child;
                    String cameraId = (String) button.getTag();
                    if (cameraId != null && cameraId.equals(currentCameraId)) {
                        // 高亮当前选中的按钮
                        button.setTextColor(ContextCompat.getColor(this, android.R.color.holo_red_light));
                        button.setBackgroundColor(ContextCompat.getColor(this, android.R.color.white));
                        button.getBackground().setAlpha(50); // 半透明背景
                        Log.d(TAG, "Highlighted button for camera: " + cameraId);
                    } else {
                        // 重置其他按钮
                        button.setTextColor(ContextCompat.getColor(this, android.R.color.white));
                        button.setBackgroundColor(ContextCompat.getColor(this, android.R.color.transparent));
                    }
                }
            }
        } else {
            // 隐藏按钮（前置摄像头或只有一个后置摄像头）
            rearCameraButtonsContainer.setVisibility(View.GONE);
        }
    }

    // 新增：更新后置摄像头按钮容器可见性 (Called from startCamera)
    private void updateRearCameraButtonVisibility() {
        boolean isCurrentCameraRear = rearCameraIds.contains(currentCameraId);

        Log.d(TAG, "更新按钮可见性 - 当前是后置: " + isCurrentCameraRear + ", 后置摄像头数量: " + rearCameraIds.size());

        if (isCurrentCameraRear && rearCameraIds.size() >= 1) {
            rearCameraButtonsContainer.setVisibility(View.VISIBLE);
            Log.d(TAG, "显示后置摄像头按钮容器");
        } else {
            rearCameraButtonsContainer.setVisibility(View.GONE);
            Log.d(TAG, "隐藏后置摄像头按钮容器");
        }
    }

    // 检查所有所需权限是否已授予
    private boolean allPermissionsGranted() {
        for (String permission : REQUIRED_PERMISSIONS) { // 遍历所有所需权限
            if (ContextCompat.checkSelfPermission( // 检查权限
                    this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false; // 如果有任何权限未授予，返回false
            }
        }
        return true; // 所有权限都已授予，返回true
    }

    // 加载最新图片缩略图
    private void loadLatestImageThumbnail() {
        // 定义查询的列
        String[] projection = new String[]{
                MediaStore.Images.Media._ID, // 图片ID
                MediaStore.Images.Media.DATA, // 图片数据路径
                MediaStore.Images.Media.DATE_ADDED // 图片添加日期
        };
        // 定义排序顺序
        String sortOrder = MediaStore.Images.Media.DATE_ADDED + " DESC"; // 按添加日期降序排列

        // 获取媒体集合URI
        Uri collection;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) { // 如果SDK版本大于等于Android Q
            collection = MediaStore.Images.Media.getContentUri( // 获取外部存储的图片内容URI
                    MediaStore.VOLUME_EXTERNAL);
        } else { // 否则
            collection = MediaStore.Images.Media.EXTERNAL_CONTENT_URI; // 获取外部存储的图片内容URI
        }

        // 执行查询
        try (Cursor cursor = getContentResolver().query( // 使用try-with-resources确保Cursor关闭
                collection, // 查询URI
                projection, // 查询列
                null, // 选择条件
                null, // 选择参数
                sortOrder // 排序顺序
        )) {
            if (cursor != null && cursor.moveToFirst()) { // 如果查询结果不为空且移动到第一行成功
                long id = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)); // 获取图片ID
                Uri contentUri = ContentUris.withAppendedId( // 构建内容URI
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);
                viewBinding.galleryThumbnail.setImageURI(contentUri); // 设置缩略图
                viewBinding.galleryThumbnail.setBackgroundColor(ContextCompat.getColor(this, android.R.color.transparent)); // 清除背景颜色
            } else { // 如果没有找到媒体文件
                // 没有找到媒体，设置背景为黑色并清除图片
                viewBinding.galleryThumbnail.setImageURI(null); // 清除任何之前的图片
                viewBinding.galleryThumbnail.setBackgroundColor(ContextCompat.getColor(this, android.R.color.black)); // 设置背景颜色为黑色
            }
        } catch (Exception e) { // 捕获异常
            Log.e(TAG, "Error loading latest image thumbnail: " + e.getMessage()); // 打印错误日志
            // 发生错误时，也设置背景为黑色
            viewBinding.galleryThumbnail.setImageURI(null); // 清除任何之前的图片
            viewBinding.galleryThumbnail.setBackgroundColor(ContextCompat.getColor(this, android.R.color.black)); // 设置背景颜色为黑色
        }
    }
 
    // 初始化滤镜功能
    private void initializeFilters() {
        // 获取滤镜管理器实例
        filterManager = FilterManager.getInstance();

        // 初始化滤镜选择器
        filterSelector = viewBinding.filterSelector;
        filterSelector.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

        // 创建普通滤镜适配器
        filterAdapter = new FilterAdapter(this, getBasicFilters());
        filterAdapter.setOnFilterSelectedListener(this::onFilterSelected);

        // 创建富士滤镜适配器
        fujiFilterAdapter = new FujiFilterAdapter(this, getFujiFilters());
        fujiFilterAdapter.setOnFilterSelectedListener(this::onFilterSelected);

        // 默认使用普通滤镜
        filterSelector.setAdapter(filterAdapter);
    }

    // 获取基础滤镜列表
    private java.util.List<FilterType> getBasicFilters() {
        java.util.List<FilterType> basicFilters = new java.util.ArrayList<>();
        basicFilters.add(FilterType.NONE);
        basicFilters.add(FilterType.VINTAGE);
        basicFilters.add(FilterType.BLACK_WHITE);
        basicFilters.add(FilterType.SEPIA);
        basicFilters.add(FilterType.COOL);
        basicFilters.add(FilterType.WARM);
        basicFilters.add(FilterType.DRAMATIC);
        basicFilters.add(FilterType.VIVID);
        basicFilters.add(FilterType.SOFT);
        return basicFilters;
    }

    // 获取富士滤镜列表
    private java.util.List<FilterType> getFujiFilters() {
        java.util.List<FilterType> fujiFilters = new java.util.ArrayList<>();
        fujiFilters.add(FilterType.NONE);
        fujiFilters.add(FilterType.FUJI_PROVIA);
        fujiFilters.add(FilterType.FUJI_VELVIA);
        fujiFilters.add(FilterType.FUJI_ASTIA);
        fujiFilters.add(FilterType.FUJI_CLASSIC_CHROME);
        fujiFilters.add(FilterType.FUJI_PRO_NEG_HI);
        fujiFilters.add(FilterType.FUJI_PRO_NEG_STD);
        fujiFilters.add(FilterType.FUJI_CLASSIC_NEG);
        return fujiFilters;
    }

    // 切换滤镜选择器的可见性
    private void toggleFilterSelector() {
        if (!isFilterSelectorVisible) {
            // 显示滤镜选择器
            isFilterSelectorVisible = true;
            filterSelector.setVisibility(View.VISIBLE);
            // 更新滤镜按钮状态
            viewBinding.filterButton.setColorFilter(ContextCompat.getColor(this, android.R.color.holo_red_light));
        } else {
            // 切换滤镜模式（普通 <-> 富士）
            toggleFilterMode();
        }
    }

    // 切换滤镜模式
    private void toggleFilterMode() {
        isFujiMode = !isFujiMode;
        if (isFujiMode) {
            filterSelector.setAdapter(fujiFilterAdapter);
            fujiFilterAdapter.setSelectedFilter(filterManager.getCurrentFilterType());
            Toast.makeText(this, "Fuji Film Simulation", Toast.LENGTH_SHORT).show();
        } else {
            filterSelector.setAdapter(filterAdapter);
            filterAdapter.setSelectedFilter(filterManager.getCurrentFilterType());
            Toast.makeText(this, "Basic Filters", Toast.LENGTH_SHORT).show();
        }
    }

    // 隐藏滤镜选择器
    private void hideFilterSelector() {
        isFilterSelectorVisible = false;
        filterSelector.setVisibility(View.GONE);
        // 恢复滤镜按钮状态
        viewBinding.filterButton.setColorFilter(ContextCompat.getColor(this, android.R.color.white));
    }

    // 初始化Extensions Manager
    private void initializeExtensionsManager() {
        if (cameraProvider == null) return;

        ListenableFuture<ExtensionsManager> extensionsManagerFuture =
            ExtensionsManager.getInstanceAsync(this, cameraProvider);

        extensionsManagerFuture.addListener(() -> {
            try {
                extensionsManager = extensionsManagerFuture.get();
                checkHdrExtensionAvailability();
            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize ExtensionsManager: " + e.getMessage());
                isHdrExtensionAvailable = false;
            }
        }, ContextCompat.getMainExecutor(this));
    }

    // 检查HDR扩展是否可用
    private void checkHdrExtensionAvailability() {
        if (extensionsManager == null || currentCameraId == null) {
            isHdrExtensionAvailable = false;
            return;
        }

        try {
            CameraSelector cameraSelector = allCameraSelectors.get(currentCameraId);
            if (cameraSelector != null) {
                isHdrExtensionAvailable = extensionsManager.isExtensionAvailable(
                    cameraSelector, ExtensionMode.HDR);
                Log.d(TAG, "HDR extension available: " + isHdrExtensionAvailable);

                // 更新HDR按钮状态
                runOnUiThread(this::updateHdrButtonAvailability);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking HDR extension availability: " + e.getMessage());
            isHdrExtensionAvailable = false;
        }
    }

    // 使用HDR扩展绑定相机
    private androidx.camera.core.Camera bindCameraWithHdrExtension(
            CameraSelector cameraSelector,
            Preview preview,
            ImageCapture imageCapture,
            VideoCapture<Recorder> videoCapture,
            ImageAnalysis imageAnalysis) {

        try {
            // 创建HDR扩展的CameraSelector
            CameraSelector hdrCameraSelector = extensionsManager.getExtensionEnabledCameraSelector(
                cameraSelector, ExtensionMode.HDR);

            Log.d(TAG, "Binding camera with HDR extension");
            return cameraProvider.bindToLifecycle(this, hdrCameraSelector,
                preview, imageCapture, videoCapture, imageAnalysis);

        } catch (Exception e) {
            Log.e(TAG, "Failed to bind camera with HDR extension: " + e.getMessage());
            // 回退到普通绑定
            return cameraProvider.bindToLifecycle(this, cameraSelector,
                preview, imageCapture, videoCapture, imageAnalysis);
        }
    }

    // 滤镜选择回调
    private void onFilterSelected(FilterType filterType) {
        filterManager.setCurrentFilter(filterType);
        // 重新启动相机以应用滤镜
        startCamera();
        Toast.makeText(this, "Filter: " + getString(filterType.getDisplayNameResId()), Toast.LENGTH_SHORT).show();
    }

    // 应用滤镜到预览
    private void applyFilterToPreview() {
        if (filterManager != null) {
            android.graphics.ColorMatrixColorFilter filter = filterManager.getCurrentColorMatrixFilter();
            if (filter != null) {
                // 注意：CameraX的PreviewView不直接支持ColorFilter
                // 这里我们可以通过其他方式实现滤镜效果，比如使用ImageAnalysis
                // 或者在拍照时应用滤镜
                Log.d(TAG, "Filter applied: " + filterManager.getCurrentFilterType().getName());
            }
        }
    }

    // Activity销毁时调用
    @Override
    protected void onDestroy() {
        super.onDestroy(); // 调用父类的onDestroy方法
        cameraExecutor.shutdown(); // 关闭相机执行器
    }
}
