package com.example.mcamera;

/**
 * 滤镜类型枚举
 * 定义了所有可用的滤镜类型
 */
public enum FilterType {
    NONE("None", R.string.filter_none),
    VINTAGE("Vintage", R.string.filter_vintage),
    BLACK_WHITE("BlackWhite", R.string.filter_black_white),
    SEPIA("Sepia", R.string.filter_sepia),
    COOL("Cool", R.string.filter_cool),
    WARM("Warm", R.string.filter_warm),
    DRAMATIC("Dramatic", R.string.filter_dramatic),
    VIVID("Vivid", R.string.filter_vivid),
    SOFT("Soft", R.string.filter_soft);

    private final String name;
    private final int displayNameResId;

    FilterType(String name, int displayNameResId) {
        this.name = name;
        this.displayNameResId = displayNameResId;
    }

    public String getName() {
        return name;
    }

    public int getDisplayNameResId() {
        return displayNameResId;
    }
}
