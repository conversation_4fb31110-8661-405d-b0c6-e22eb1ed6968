package com.example.mcamera;

import android.graphics.Color;

/**
 * 色彩处理工具类
 * 提供高级的色彩调整功能，用于实现富士胶片模拟效果
 */
public class ColorUtils {

    /**
     * RGB转HSV
     */
    public static float[] rgbToHsv(int r, int g, int b) {
        float[] hsv = new float[3];
        Color.RGBToHSV(r, g, b, hsv);
        return hsv;
    }

    /**
     * HSV转RGB
     */
    public static int hsvToRgb(float[] hsv) {
        return Color.HSVToColor(hsv);
    }

    /**
     * 调整饱和度
     */
    public static int adjustSaturation(int color, float saturationFactor) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);

        float[] hsv = rgbToHsv(r, g, b);
        hsv[1] = Math.max(0, Math.min(1, hsv[1] * saturationFactor));
        
        int newColor = hsvToRgb(hsv);
        return Color.argb(a, Color.red(newColor), Color.green(newColor), Color.blue(newColor));
    }

    /**
     * 调整色相
     */
    public static int adjustHue(int color, float hueShift) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);

        float[] hsv = rgbToHsv(r, g, b);
        hsv[0] = (hsv[0] + hueShift) % 360;
        if (hsv[0] < 0) hsv[0] += 360;
        
        int newColor = hsvToRgb(hsv);
        return Color.argb(a, Color.red(newColor), Color.green(newColor), Color.blue(newColor));
    }

    /**
     * 调整亮度
     */
    public static int adjustBrightness(int color, float brightnessFactor) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);

        float[] hsv = rgbToHsv(r, g, b);
        hsv[2] = Math.max(0, Math.min(1, hsv[2] * brightnessFactor));
        
        int newColor = hsvToRgb(hsv);
        return Color.argb(a, Color.red(newColor), Color.green(newColor), Color.blue(newColor));
    }

    /**
     * 应用S曲线调整对比度
     */
    public static int applySCurve(int color, float strength) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);

        r = (int) applySCurveToChannel(r / 255.0f, strength) * 255;
        g = (int) applySCurveToChannel(g / 255.0f, strength) * 255;
        b = (int) applySCurveToChannel(b / 255.0f, strength) * 255;

        r = Math.max(0, Math.min(255, r));
        g = Math.max(0, Math.min(255, g));
        b = Math.max(0, Math.min(255, b));

        return Color.argb(a, r, g, b);
    }

    private static float applySCurveToChannel(float value, float strength) {
        // S曲线公式: y = x + strength * sin(2π * x) / (2π)
        return value + strength * (float) Math.sin(2 * Math.PI * value) / (2 * (float) Math.PI);
    }

    /**
     * 色彩分离效果（高光和阴影分别调色）
     */
    public static int colorGrading(int color, int highlightColor, int shadowColor, float threshold) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);

        // 计算亮度
        float luminance = (0.299f * r + 0.587f * g + 0.114f * b) / 255.0f;
        
        float highlightWeight = luminance > threshold ? (luminance - threshold) / (1.0f - threshold) : 0;
        float shadowWeight = luminance < threshold ? (threshold - luminance) / threshold : 0;

        // 混合颜色
        int hr = Color.red(highlightColor);
        int hg = Color.green(highlightColor);
        int hb = Color.blue(highlightColor);
        
        int sr = Color.red(shadowColor);
        int sg = Color.green(shadowColor);
        int sb = Color.blue(shadowColor);

        r = (int) (r + highlightWeight * (hr - 128) * 0.3f + shadowWeight * (sr - 128) * 0.3f);
        g = (int) (g + highlightWeight * (hg - 128) * 0.3f + shadowWeight * (sg - 128) * 0.3f);
        b = (int) (b + highlightWeight * (hb - 128) * 0.3f + shadowWeight * (sb - 128) * 0.3f);

        r = Math.max(0, Math.min(255, r));
        g = Math.max(0, Math.min(255, g));
        b = Math.max(0, Math.min(255, b));

        return Color.argb(a, r, g, b);
    }

    /**
     * 胶片颗粒效果
     */
    public static int addFilmGrain(int color, float intensity) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);

        // 简单的随机噪声
        float noise = (float) (Math.random() - 0.5) * intensity * 20;
        
        r = Math.max(0, Math.min(255, r + (int) noise));
        g = Math.max(0, Math.min(255, g + (int) noise));
        b = Math.max(0, Math.min(255, b + (int) noise));

        return Color.argb(a, r, g, b);
    }

    /**
     * 温度调整（色温）
     */
    public static int adjustTemperature(int color, float temperature) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);

        if (temperature > 0) {
            // 暖色调
            r = Math.min(255, (int) (r * (1 + temperature * 0.3f)));
            b = Math.max(0, (int) (b * (1 - temperature * 0.2f)));
        } else {
            // 冷色调
            b = Math.min(255, (int) (b * (1 - temperature * 0.3f)));
            r = Math.max(0, (int) (r * (1 + temperature * 0.2f)));
        }

        return Color.argb(a, r, g, b);
    }
}
