<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity"
    android:background="@android:color/black">

    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Top Control Bar -->
    <LinearLayout
        android:id="@+id/topControlBar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:background="#80000000"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/flash_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_flash"
            android:tint="@android:color/white"
            android:contentDescription="Flash" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- Mode Selector (Moved inside Top Control Bar) -->
        <LinearLayout
            android:id="@+id/modeSelector"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="#00000000"
            android:padding="0dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal">
                <TextView
                    android:id="@+id/mode_video"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="录像"
                    android:textColor="@android:color/white"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />
                <View
                    android:id="@+id/indicator_video"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:background="@drawable/red_dot_indicator"
                    android:visibility="invisible" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal">
                <TextView
                    android:id="@+id/mode_photo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="拍照"
                    android:textColor="@android:color/holo_red_light"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />
                <View
                    android:id="@+id/indicator_photo"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:background="@drawable/red_dot_indicator"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/hdr_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_hdr"
            android:tint="@android:color/white"
            android:contentDescription="HDR" />

        <ImageView
            android:id="@+id/leica_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_leica_logo"
            android:tint="@android:color/white"
            android:contentDescription="Leica" />
    </LinearLayout>

    <!-- Container for Rear Camera Buttons -->
    <LinearLayout
        android:id="@+id/rearCameraButtonsContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="8dp"
        android:background="#80000000"
        app:layout_constraintBottom_toTopOf="@+id/zoomSelector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="8dp"
        android:visibility="gone" /> <!-- Initially hidden -->

    <!-- Zoom Selector (Adjusted position) -->
    <LinearLayout
        android:id="@+id/zoomSelector"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#80000000"
        android:padding="8dp"
        app:layout_constraintBottom_toTopOf="@+id/zoomSeekBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="8dp">

        <TextView
            android:id="@+id/zoom_0_6x"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.6"
            android:textColor="@android:color/white"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />

        <TextView
            android:id="@+id/zoom_1x"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1X"
            android:textColor="@android:color/holo_red_light"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />

        <TextView
            android:id="@+id/zoom_2x"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2"
            android:textColor="@android:color/white"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />

        <TextView
            android:id="@+id/zoom_3_2x"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3.2"
            android:textColor="@android:color/white"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />
    </LinearLayout>

    <!-- Zoom Seek Bar -->
    <SeekBar
        android:id="@+id/zoomSeekBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:progressTint="@android:color/holo_red_light"
        android:thumbTint="@android:color/holo_red_light"
        app:layout_constraintBottom_toTopOf="@+id/bottomControlBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Zoom Level TextView -->
    <TextView
        android:id="@+id/zoomLevelTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:layout_marginBottom="4dp"
        app:layout_constraintBottom_toTopOf="@+id/zoomSeekBar"
        app:layout_constraintEnd_toEndOf="@+id/zoomSeekBar"
        tools:text="1.0x" />

    <!-- Bottom Control Bar -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottomControlBar"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@android:color/black"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/gallery_thumbnail"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="32dp"
            android:src="@android:drawable/ic_menu_gallery"
            android:background="@android:color/darker_gray"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:contentDescription="Gallery Thumbnail" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/camera_capture_button"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:clickable="true"
            android:focusable="true"
            android:src="@android:drawable/ic_menu_camera"
            android:background="@drawable/ic_shutter_button_background"
            app:fabCustomSize="72dp"
            app:maxImageSize="48dp"
            app:tint="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:contentDescription="Capture Button" />

        <ImageView
            android:id="@+id/camera_switch_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="32dp"
            android:src="@drawable/ic_camera_switch"
            android:tint="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:contentDescription="Switch Camera" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
