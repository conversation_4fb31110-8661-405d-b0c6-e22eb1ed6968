<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground">

    <!-- 富士风格的滤镜预览框 -->
    <FrameLayout
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:background="@drawable/fuji_filter_preview_background">

        <ImageView
            android:id="@+id/filter_preview"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:scaleType="centerCrop"
            android:contentDescription="Filter Preview" />

        <!-- 富士标识小点 -->
        <View
            android:id="@+id/fuji_indicator"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_gravity="top|end"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/fuji_indicator_dot"
            android:visibility="gone" />

    </FrameLayout>

    <!-- 滤镜名称 -->
    <TextView
        android:id="@+id/filter_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="Provia"
        android:textColor="@android:color/white"
        android:textSize="11sp"
        android:fontFamily="monospace"
        android:gravity="center" />

    <!-- 富士胶片类型标识 -->
    <TextView
        android:id="@+id/film_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="RDPIII"
        android:textColor="#80FFFFFF"
        android:textSize="8sp"
        android:fontFamily="monospace"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>
