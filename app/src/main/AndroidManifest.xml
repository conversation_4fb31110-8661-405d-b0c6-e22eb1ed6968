<?xml version="1.0" encoding="utf-8"?>
<!-- 定义Android应用的清单文件，声明应用的包名、组件、权限等信息 -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 声明应用使用相机功能，any表示可以使用任何相机（前置或后置） -->
    <uses-feature android:name="android.hardware.camera.any" />
    <!-- 声明应用需要相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 声明应用需要录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!-- 声明应用需要写入外部存储权限，maxSdkVersion="28"表示此权限只在API级别28及以下有效 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- 应用标签，包含应用的全局设置 -->
    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MCamera"
        tools:targetApi="31">
        <!-- Activity声明，定义应用的主Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <!-- Intent过滤器，使MainActivity成为应用的入口点 -->
            <intent-filter>
                <!-- 定义主入口动作 -->
                <action android:name="android.intent.action.MAIN" />
                <!-- 定义启动器类别，使应用图标显示在设备的应用列表中 -->
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>
