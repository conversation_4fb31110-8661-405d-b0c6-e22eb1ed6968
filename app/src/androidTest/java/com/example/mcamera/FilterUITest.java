package com.example.mcamera;

import androidx.test.ext.junit.rules.ActivityScenarioRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.espresso.Espresso;
import androidx.test.espresso.action.ViewActions;
import androidx.test.espresso.assertion.ViewAssertions;
import androidx.test.espresso.matcher.ViewMatchers;

import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * 滤镜UI集成测试
 */
@RunWith(AndroidJUnit4.class)
public class FilterUITest {

    @Rule
    public ActivityScenarioRule<MainActivity> activityRule = 
        new ActivityScenarioRule<>(MainActivity.class);

    @Test
    public void testFilterButtonExists() {
        // 验证滤镜按钮存在
        Espresso.onView(ViewMatchers.withId(R.id.filter_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }

    @Test
    public void testFilterSelectorToggle() {
        // 点击滤镜按钮
        Espresso.onView(ViewMatchers.withId(R.id.filter_button))
                .perform(ViewActions.click());

        // 验证滤镜选择器显示
        Espresso.onView(ViewMatchers.withId(R.id.filterSelector))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));

        // 再次点击滤镜按钮
        Espresso.onView(ViewMatchers.withId(R.id.filter_button))
                .perform(ViewActions.click());

        // 验证滤镜选择器隐藏
        Espresso.onView(ViewMatchers.withId(R.id.filterSelector))
                .check(ViewAssertions.matches(ViewMatchers.withEffectiveVisibility(ViewMatchers.Visibility.GONE)));
    }

    @Test
    public void testCameraCaptureButtonExists() {
        // 验证拍照按钮存在
        Espresso.onView(ViewMatchers.withId(R.id.camera_capture_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }

    @Test
    public void testTopBarButtonsExist() {
        // 验证顶部栏按钮存在
        Espresso.onView(ViewMatchers.withId(R.id.flash_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
        
        Espresso.onView(ViewMatchers.withId(R.id.hdr_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
        
        Espresso.onView(ViewMatchers.withId(R.id.filter_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }
}
