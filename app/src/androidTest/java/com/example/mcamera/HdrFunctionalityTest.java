package com.example.mcamera;

import androidx.test.ext.junit.rules.ActivityScenarioRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.espresso.Espresso;
import androidx.test.espresso.action.ViewActions;
import androidx.test.espresso.assertion.ViewAssertions;
import androidx.test.espresso.matcher.ViewMatchers;

import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * HDR功能集成测试
 */
@RunWith(AndroidJUnit4.class)
public class HdrFunctionalityTest {

    @Rule
    public ActivityScenarioRule<MainActivity> activityRule = 
        new ActivityScenarioRule<>(MainActivity.class);

    @Test
    public void testHdrButtonExists() {
        // 验证HDR按钮存在
        Espresso.onView(ViewMatchers.withId(R.id.hdr_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }

    @Test
    public void testHdrButtonClickable() {
        // 等待相机初始化
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 点击HDR按钮
        Espresso.onView(ViewMatchers.withId(R.id.hdr_button))
                .perform(ViewActions.click());

        // 验证按钮仍然可见（无论HDR是否支持）
        Espresso.onView(ViewMatchers.withId(R.id.hdr_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }

    @Test
    public void testCameraFunctionalityWithHdr() {
        // 等待相机初始化
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 尝试启用HDR
        Espresso.onView(ViewMatchers.withId(R.id.hdr_button))
                .perform(ViewActions.click());

        // 验证拍照按钮仍然可用
        Espresso.onView(ViewMatchers.withId(R.id.camera_capture_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
                .check(ViewAssertions.matches(ViewMatchers.isEnabled()));
    }
}
