package com.example.mcamera;

import androidx.test.ext.junit.rules.ActivityScenarioRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.espresso.Espresso;
import androidx.test.espresso.action.ViewActions;
import androidx.test.espresso.assertion.ViewAssertions;
import androidx.test.espresso.matcher.ViewMatchers;

import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * 后置摄像头切换功能测试
 */
@RunWith(AndroidJUnit4.class)
public class RearCameraSwitchTest {

    @Rule
    public ActivityScenarioRule<MainActivity> activityRule = 
        new ActivityScenarioRule<>(MainActivity.class);

    @Test
    public void testCameraSwitchButtonExists() {
        // 验证相机切换按钮存在
        Espresso.onView(ViewMatchers.withId(R.id.camera_switch_button))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }

    @Test
    public void testCameraSwitchFunctionality() {
        // 等待相机初始化
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 点击相机切换按钮
        Espresso.onView(ViewMatchers.withId(R.id.camera_switch_button))
                .perform(ViewActions.click());

        // 验证相机预览仍然可见
        Espresso.onView(ViewMatchers.withId(R.id.viewFinder))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }

    @Test
    public void testRearCameraButtonsContainer() {
        // 等待相机初始化
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 验证后置摄像头按钮容器存在
        Espresso.onView(ViewMatchers.withId(R.id.rearCameraButtonsContainer))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
    }

    @Test
    public void testMultipleCameraSwitches() {
        // 等待相机初始化
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 连续点击相机切换按钮多次
        for (int i = 0; i < 3; i++) {
            Espresso.onView(ViewMatchers.withId(R.id.camera_switch_button))
                    .perform(ViewActions.click());
            
            // 短暂等待相机切换完成
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            
            // 验证相机预览仍然正常
            Espresso.onView(ViewMatchers.withId(R.id.viewFinder))
                    .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));
        }
    }
}
