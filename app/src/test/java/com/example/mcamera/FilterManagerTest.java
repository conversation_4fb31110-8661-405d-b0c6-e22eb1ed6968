package com.example.mcamera;

import android.graphics.Bitmap;
import android.graphics.ColorMatrix;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

/**
 * 滤镜管理器单元测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class FilterManagerTest {

    private FilterManager filterManager;

    @Before
    public void setUp() {
        filterManager = FilterManager.getInstance();
    }

    @Test
    public void testFilterManagerSingleton() {
        FilterManager instance1 = FilterManager.getInstance();
        FilterManager instance2 = FilterManager.getInstance();
        assertSame("FilterManager should be singleton", instance1, instance2);
    }

    @Test
    public void testDefaultFilter() {
        assertEquals("Default filter should be NONE", FilterType.NONE, filterManager.getCurrentFilterType());
    }

    @Test
    public void testSetCurrentFilter() {
        filterManager.setCurrentFilter(FilterType.BLACK_WHITE);
        assertEquals("Current filter should be BLACK_WHITE", FilterType.BLACK_WHITE, filterManager.getCurrentFilterType());
        
        filterManager.setCurrentFilter(FilterType.VINTAGE);
        assertEquals("Current filter should be VINTAGE", FilterType.VINTAGE, filterManager.getCurrentFilterType());
    }

    @Test
    public void testGetAllFilterTypes() {
        assertNotNull("Filter types list should not be null", filterManager.getAllFilterTypes());
        assertTrue("Should have multiple filter types", filterManager.getAllFilterTypes().size() > 1);
        assertTrue("Should contain NONE filter", filterManager.getAllFilterTypes().contains(FilterType.NONE));
        assertTrue("Should contain BLACK_WHITE filter", filterManager.getAllFilterTypes().contains(FilterType.BLACK_WHITE));
    }

    @Test
    public void testGetFilterProcessor() {
        FilterProcessor noneProcessor = filterManager.getFilterProcessor(FilterType.NONE);
        assertNotNull("NONE filter processor should not be null", noneProcessor);
        assertEquals("Processor should return correct filter type", FilterType.NONE, noneProcessor.getFilterType());

        FilterProcessor blackWhiteProcessor = filterManager.getFilterProcessor(FilterType.BLACK_WHITE);
        assertNotNull("BLACK_WHITE filter processor should not be null", blackWhiteProcessor);
        assertEquals("Processor should return correct filter type", FilterType.BLACK_WHITE, blackWhiteProcessor.getFilterType());
    }

    @Test
    public void testColorMatrixForBlackWhiteFilter() {
        filterManager.setCurrentFilter(FilterType.BLACK_WHITE);
        ColorMatrix colorMatrix = filterManager.getCurrentColorMatrix();
        assertNotNull("BLACK_WHITE filter should have color matrix", colorMatrix);
    }

    @Test
    public void testColorMatrixForNoneFilter() {
        filterManager.setCurrentFilter(FilterType.NONE);
        ColorMatrix colorMatrix = filterManager.getCurrentColorMatrix();
        assertNull("NONE filter should not have color matrix", colorMatrix);
    }

    @Test
    public void testFilterProcessorTypes() {
        // 测试所有滤镜类型都有对应的处理器
        for (FilterType filterType : FilterType.values()) {
            FilterProcessor processor = filterManager.getFilterProcessor(filterType);
            assertNotNull("Filter processor should exist for " + filterType.getName(), processor);
            assertEquals("Processor should return correct type", filterType, processor.getFilterType());
        }
    }
}
