package com.example.mcamera;

import android.graphics.Bitmap;
import android.graphics.Color;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

/**
 * 富士滤镜功能测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class FujiFilterTest {

    private FilterManager filterManager;
    private Bitmap testBitmap;

    @Before
    public void setUp() {
        filterManager = FilterManager.getInstance();
        // 创建测试用的位图
        testBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888);
        testBitmap.eraseColor(Color.GRAY);
    }

    @Test
    public void testFujiFilterTypesExist() {
        // 测试所有富士滤镜类型都存在
        assertNotNull("Provia filter should exist", 
            filterManager.getFilterProcessor(FilterType.FUJI_PROVIA));
        assertNotNull("Velvia filter should exist", 
            filterManager.getFilterProcessor(FilterType.FUJI_VELVIA));
        assertNotNull("Astia filter should exist", 
            filterManager.getFilterProcessor(FilterType.FUJI_ASTIA));
        assertNotNull("Classic Chrome filter should exist", 
            filterManager.getFilterProcessor(FilterType.FUJI_CLASSIC_CHROME));
        assertNotNull("Pro Neg Hi filter should exist", 
            filterManager.getFilterProcessor(FilterType.FUJI_PRO_NEG_HI));
        assertNotNull("Pro Neg Std filter should exist", 
            filterManager.getFilterProcessor(FilterType.FUJI_PRO_NEG_STD));
        assertNotNull("Classic Neg filter should exist", 
            filterManager.getFilterProcessor(FilterType.FUJI_CLASSIC_NEG));
    }

    @Test
    public void testProviaFilter() {
        filterManager.setCurrentFilter(FilterType.FUJI_PROVIA);
        FilterProcessor processor = filterManager.getCurrentFilterProcessor();
        
        assertNotNull("Provia processor should not be null", processor);
        assertEquals("Should be Provia filter", FilterType.FUJI_PROVIA, processor.getFilterType());
        
        // 测试滤镜应用
        Bitmap result = processor.applyFilter(testBitmap);
        assertNotNull("Filtered bitmap should not be null", result);
        assertNotSame("Result should be different from original", testBitmap, result);
    }

    @Test
    public void testVelviaFilter() {
        filterManager.setCurrentFilter(FilterType.FUJI_VELVIA);
        FilterProcessor processor = filterManager.getCurrentFilterProcessor();
        
        assertNotNull("Velvia processor should not be null", processor);
        assertEquals("Should be Velvia filter", FilterType.FUJI_VELVIA, processor.getFilterType());
        
        // Velvia应该有颜色矩阵
        assertNotNull("Velvia should have color matrix", processor.getColorMatrix());
    }

    @Test
    public void testClassicChromeFilter() {
        filterManager.setCurrentFilter(FilterType.FUJI_CLASSIC_CHROME);
        FilterProcessor processor = filterManager.getCurrentFilterProcessor();
        
        assertNotNull("Classic Chrome processor should not be null", processor);
        assertEquals("Should be Classic Chrome filter", 
            FilterType.FUJI_CLASSIC_CHROME, processor.getFilterType());
    }

    @Test
    public void testColorUtilsSaturationAdjustment() {
        int originalColor = Color.rgb(128, 128, 128);
        int adjustedColor = ColorUtils.adjustSaturation(originalColor, 1.5f);
        
        assertNotEquals("Color should be different after saturation adjustment", 
            originalColor, adjustedColor);
    }

    @Test
    public void testColorUtilsHueAdjustment() {
        int originalColor = Color.rgb(255, 0, 0); // Red
        int adjustedColor = ColorUtils.adjustHue(originalColor, 120); // Should shift towards green
        
        assertNotEquals("Color should be different after hue adjustment", 
            originalColor, adjustedColor);
    }

    @Test
    public void testColorUtilsBrightnessAdjustment() {
        int originalColor = Color.rgb(128, 128, 128);
        int brighterColor = ColorUtils.adjustBrightness(originalColor, 1.5f);
        int darkerColor = ColorUtils.adjustBrightness(originalColor, 0.5f);
        
        assertNotEquals("Brighter color should be different", originalColor, brighterColor);
        assertNotEquals("Darker color should be different", originalColor, darkerColor);
        assertNotEquals("Brighter and darker should be different", brighterColor, darkerColor);
    }

    @Test
    public void testFilterPerformance() {
        filterManager.setCurrentFilter(FilterType.FUJI_VELVIA);
        
        long startTime = System.currentTimeMillis();
        Bitmap result = filterManager.applyCurrentFilter(testBitmap);
        long endTime = System.currentTimeMillis();
        
        assertNotNull("Result should not be null", result);
        assertTrue("Filter should complete within reasonable time (< 1000ms)", 
            (endTime - startTime) < 1000);
    }

    @Test
    public void testFilterChaining() {
        // 测试连续应用不同滤镜
        filterManager.setCurrentFilter(FilterType.FUJI_PROVIA);
        Bitmap step1 = filterManager.applyCurrentFilter(testBitmap);
        
        filterManager.setCurrentFilter(FilterType.FUJI_VELVIA);
        Bitmap step2 = filterManager.applyCurrentFilter(step1);
        
        assertNotNull("Step 1 result should not be null", step1);
        assertNotNull("Step 2 result should not be null", step2);
        assertNotSame("Results should be different", step1, step2);
    }
}
