plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.example.mcamera'
    compileSdk 35

    defaultConfig {
        applicationId "com.example.mcamera"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.camera:camera-core:1.5.0-beta01'
    implementation 'androidx.camera:camera-camera2:1.5.0-beta01'
    implementation 'androidx.camera:camera-lifecycle:1.5.0-beta01'
    implementation 'androidx.camera:camera-video:1.5.0-beta01'
    implementation 'androidx.camera:camera-view:1.5.0-beta01'
    implementation 'androidx.camera:camera-extensions:1.5.0-beta01'

    testImplementation libs.junit
    testImplementation 'org.robolectric:robolectric:4.10.3'
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}
